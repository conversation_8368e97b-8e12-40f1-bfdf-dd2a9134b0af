# -*- coding: utf-8 -*-
"""
Created on Wed Jan 15 13:48:14 2025

@author: h<PERSON>nan<PERSON>
"""

import os
import zipfile
import boto3
import pandas as pd
from datetime import datetime, timedelta
import sys
import re
from config import bucket_s3,input_path, private_key_path, public_key_path
from file_signer.file_signer import FileSigner


#Llega como parametro 2025/03/11
input_date = sys.argv[1]
data_day = datetime.strptime(input_date, '%Y/%m/%d') + timedelta(days=1)
path_day = data_day.strftime('%Y-%m-%d')
data_day = data_day.strftime('%Y%m%d')


#Fecha de ejecucion
full_datetime = (datetime.now() + timedelta(days=0))
process_time = full_datetime.strftime('%H%M')
#data_day = full_datetime.strftime('%Y%m%d')

def upload_s3(file_route, file_name, s3_key):
    s3_client = boto3.client('s3')
    s3_client.upload_file(file_route, bucket_s3, f"{s3_key}{file_name}")
    print(f"Archivo: {file_name} subido a: s3://{bucket_s3}/{s3_key}{file_name}")

# Construir el nombre del archivo de entrada
input_file = f"{input_path}/TR-{data_day}.csv"

# Verificar si el archivo existe
if not os.path.exists(input_file):
    print(f"Error: El archivo {input_file} no existe.")
    sys.exit(1)

# Verificar si el archivo está vacío
if os.path.getsize(input_file) == 0:
    print(f"Error: El archivo {input_file} está vacío.")
    sys.exit(1)

# Leer el archivo CSV original
try:
    df = pd.read_csv(input_file, dtype=str)
    if df.empty:
        print(f"Error: El archivo {input_file} no contiene datos válidos.")
        sys.exit(1)
except pd.errors.EmptyDataError:
    print(f"Error: El archivo {input_file} no tiene columnas válidas para procesar.")
    sys.exit(1)
except Exception as e:
    print(f"Error al leer el archivo {input_file}: {str(e)}")
    sys.exit(1)

df.to_csv('DATA_FILTRADA.csv',index=False)

# Asegurarnos de que el campo 'DateTime' está en el formato adecuado
df['DateTime'] = pd.to_datetime(df['DateTime'], errors='coerce')

# Generar el ID de transacción con el formato 'YYYYMMDD' seguido del correlativo de 8 dígitos
#df['TransactionID'] = [
#    f'{row.DateTime.strftime("%Y%m%d")}3{i:06d}' for i, row in enumerate(df.itertuples(), 1)
#]

df['TransactionID'] = df['FinancialTransactionID']

# Función para extraer la primera parte antes del espacio en ToProfile o FromProfile
def extract_prefix(profile):
    return profile.split(' ')[0] if isinstance(profile, str) and profile.strip() else None

# Establecer un conjunto para almacenar los perfiles únicos
profile_set = set()

# Primero, encontramos todos los perfiles únicos que necesitamos procesar
for _, row in df.iterrows():
    # Extraer los prefijos solo si ToProfile o FromProfile no están vacíos
    to_profile_prefix = extract_prefix(row['From_BankDomain'])
    from_profile_prefix = extract_prefix(row['To_BankDomain'])

    # Añadir los perfiles únicos al conjunto, solo si no son None (vacíos)
    if to_profile_prefix:
        profile_set.add(to_profile_prefix)
    if from_profile_prefix:
        profile_set.add(from_profile_prefix)

# Convertir el conjunto a una lista (sin duplicados)
profile_list = list(profile_set)


# Para cada perfil único, generar un archivo CSV
for profile in profile_list:
    # Filtrar los registros que contienen el perfil en ToProfile o FromProfile
    group = df[(df['From_BankDomain'].apply(extract_prefix) == profile) |
               (df['To_BankDomain'].apply(extract_prefix) == profile)]
    
    # Ordenar los registros por la columna DateTime
    group = group.sort_values(by='DateTime')
    # Agrupar por hora de la columna DateTime
    group['Hour'] = group['DateTime'].dt.hour  # Extraemos la hora de DateTime
    
    # Formatear la columna 'Amount' a 2 decimales
    if 'Amount' in group.columns:
        group['Amount'] = group['Amount'].astype(float).map('{:.2f}'.format)
        
    # Formatear la columna 'FromFee' a 2 decimales
    if 'FromFee' in group.columns:
        group['FromFee'] = group['FromFee'].astype(float).map('{:.2f}'.format)
        
    # Formatear la columna 'ToFee' a 2 decimales
    if 'ToFee' in group.columns:
        group['ToFee'] = group['ToFee'].astype(float).map('{:.2f}'.format)
        
    group = group.drop(columns=['From_BankDomain','To_BankDomain'])
    
    # Para cada hora, generar un archivo CSV
    for hour, hour_group in group.groupby('Hour'):
        # Eliminar la columna 'Hour' antes de guardar el archivo
        hour_group = hour_group.drop(columns=['Hour'])
        
        # Crear el nombre del archivo con el formato TR{Profile}_YYYYMMDDHH.csv
        output_name_file = f"TR{profile}-{data_day}{process_time}{hour:02d}.csv"
        
        output_file = f"output/{data_day}/{output_name_file}"
        
        # Guardar el DataFrame filtrado en un nuevo archivo CSV
        hour_group.to_csv(output_file, index=False, header=False)
        print(f"Archivo generado: {output_file}")
        
        profile_path = re.sub(r'[^A-Za-z]', '', profile) 
        # Subir el archivo a S3
        s3_key = f"{profile}/{path_day}/LOG-TRANSACCIONES/"
        upload_s3(output_file, os.path.basename(output_file), s3_key)
        

        signer = FileSigner(private_key_path)
        signer.sign_file(output_file)
        signature_path = f"{output_file}.signature"
        is_valid = signer.verify_signature(output_file, signature_path, public_key_path)
        signature_file = os.path.basename(output_file) + ".signature"
        if is_valid:
            upload_s3(signature_path, signature_file, s3_key)

# Ahora vamos a generar archivos separados solo por hora, sin tener en cuenta los perfiles

# Agrupar por hora en el dataframe completo
df['Hour'] = df['DateTime'].dt.hour  # Extraemos la hora de DateTime

# Formatear las columnas 'Amount', 'FromFee' y 'ToFee' a 2 decimales en el dataframe completo
if 'Amount' in df.columns:
    df['Amount'] = df['Amount'].astype(float).map('{:.2f}'.format)
    
if 'FromFee' in df.columns:
    df['FromFee'] = df['FromFee'].astype(float).map('{:.2f}'.format)

if 'ToFee' in df.columns:
    df['ToFee'] = df['ToFee'].astype(float).map('{:.2f}'.format)
    
# Agregar la columna 'version' con valor 3.0 al principio del DataFrame
df.insert(0, 'version', '3.0')

df = df.drop(columns=['From_BankDomain','To_BankDomain'])


folder_bcrp = f"BCRP/{data_day}"

os.makedirs(folder_bcrp, exist_ok=True)

# Para cada hora, generar un archivo CSV sin filtrar por perfil
for hour, hour_group in df.groupby('Hour'):
    # Eliminar la columna 'Hour' antes de guardar el archivo
    hour_group = hour_group.drop(columns=['Hour'])
    
    # Crear el nombre del archivo con el formato TR_YYYYMMDDHH.csv
    output_name_file = f"TR-{data_day}_{hour:02d}00_btr.csv"
    
    output_file = f"{folder_bcrp}/{output_name_file}"
    
    # Guardar el DataFrame filtrado en un nuevo archivo CSV
    hour_group.to_csv(output_file, index=False, header=True)
    print(f"Archivo generado (sin perfil): {output_file}")

zip_filename = f"BCRP/transactions-{data_day}.zip"

# Comprimir todo el contenido de la carpeta en el archivo ZIP
with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
    for foldername, subfolders, filenames in os.walk(folder_bcrp):
        for filename in filenames:
            file_path = os.path.join(foldername, filename)
            zipf.write(file_path, os.path.relpath(file_path, folder_bcrp))

print(f"Todos los archivos se han comprimido correctamente en: {zip_filename}")


#Enviar ZIP a S3
s3_key = f"BCRP/{path_day}/BCRP-TRANSACCIONES/"
upload_s3(zip_filename, os.path.basename(zip_filename), s3_key)
