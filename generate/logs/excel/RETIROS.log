/home/<USER>/generate/exports_excel/export_to_excel.py:28: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, con=connection)
2025-06-13 06:36:35,777 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:35,811 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:35,811 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-FCOMPARTAMOS-20250613013635.xls
2025-06-13 06:36:35,812 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-FCOMPARTAMOS-20250613013635.xls.signature
2025-06-13 06:36:35,812 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-FCOMPARTAMOS-20250613013635.xls with signature: /home/<USER>/output/excel/RETIROS-FCOMPARTAMOS-20250613013635.xls.signature
2025-06-13 06:36:35,812 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-13 06:36:35,812 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-FCOMPARTAMOS-20250613013635.xls
2025-06-13 06:36:35,842 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-13 06:36:35,985 - root - INFO - Archivo: RETIROS-FCOMPARTAMOS-20250613013635.xls.signature subido a: s3://prd-datalake-reports-637423440311/FCOMPARTAMOS/2025-06-13/RETIROS/RETIROS-FCOMPARTAMOS-20250613013635.xls.signature
2025-06-13 06:36:36,089 - root - INFO - Archivo: RETIROS-FCOMPARTAMOS-20250613013635.xls subido a: s3://prd-datalake-reports-637423440311/FCOMPARTAMOS/2025-06-13/RETIROS/RETIROS-FCOMPARTAMOS-20250613013635.xls
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='FCOMPARTAMOS'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
2025-06-13 06:36:36,174 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:36,208 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:36,208 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-BNACION-20250613013635.xls
2025-06-13 06:36:36,208 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-BNACION-20250613013635.xls.signature
2025-06-13 06:36:36,208 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-BNACION-20250613013635.xls with signature: /home/<USER>/output/excel/RETIROS-BNACION-20250613013635.xls.signature
2025-06-13 06:36:36,208 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-13 06:36:36,209 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-BNACION-20250613013635.xls
2025-06-13 06:36:36,291 - root - INFO - Archivo: RETIROS-BNACION-20250613013635.xls.signature subido a: s3://prd-datalake-reports-637423440311/BNACION/2025-06-13/RETIROS/RETIROS-BNACION-20250613013635.xls.signature
2025-06-13 06:36:36,370 - root - INFO - Archivo: RETIROS-BNACION-20250613013635.xls subido a: s3://prd-datalake-reports-637423440311/BNACION/2025-06-13/RETIROS/RETIROS-BNACION-20250613013635.xls
2025-06-13 06:36:36,456 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:36,489 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:36,489 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-CRANDES-20250613013635.xls
2025-06-13 06:36:36,490 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-CRANDES-20250613013635.xls.signature
2025-06-13 06:36:36,490 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-CRANDES-20250613013635.xls with signature: /home/<USER>/output/excel/RETIROS-CRANDES-20250613013635.xls.signature
2025-06-13 06:36:36,490 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-13 06:36:36,490 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-CRANDES-20250613013635.xls
2025-06-13 06:36:36,570 - root - INFO - Archivo: RETIROS-CRANDES-20250613013635.xls.signature subido a: s3://prd-datalake-reports-637423440311/CRANDES/2025-06-13/RETIROS/RETIROS-CRANDES-20250613013635.xls.signature
2025-06-13 06:36:36,648 - root - INFO - Archivo: RETIROS-CRANDES-20250613013635.xls subido a: s3://prd-datalake-reports-637423440311/CRANDES/2025-06-13/RETIROS/RETIROS-CRANDES-20250613013635.xls
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='BNACION'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='CRANDES'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
2025-06-13 06:36:36,741 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:36,774 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:36,774 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-CCUSCO-20250613013635.xls
2025-06-13 06:36:36,774 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-CCUSCO-20250613013635.xls.signature
2025-06-13 06:36:36,774 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-CCUSCO-20250613013635.xls with signature: /home/<USER>/output/excel/RETIROS-CCUSCO-20250613013635.xls.signature
2025-06-13 06:36:36,774 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-13 06:36:36,775 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-CCUSCO-20250613013635.xls
2025-06-13 06:36:36,850 - root - INFO - Archivo: RETIROS-CCUSCO-20250613013635.xls.signature subido a: s3://prd-datalake-reports-637423440311/CCUSCO/2025-06-13/RETIROS/RETIROS-CCUSCO-20250613013635.xls.signature
2025-06-13 06:36:36,928 - root - INFO - Archivo: RETIROS-CCUSCO-20250613013635.xls subido a: s3://prd-datalake-reports-637423440311/CCUSCO/2025-06-13/RETIROS/RETIROS-CCUSCO-20250613013635.xls
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='CCUSCO'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
2025-06-13 06:36:37,009 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:37,042 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:37,042 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-0231FCONFIANZA-20250613013635.xls
2025-06-13 06:36:37,043 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-0231FCONFIANZA-20250613013635.xls.signature
2025-06-13 06:36:37,043 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-0231FCONFIANZA-20250613013635.xls with signature: /home/<USER>/output/excel/RETIROS-0231FCONFIANZA-20250613013635.xls.signature
2025-06-13 06:36:37,043 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-13 06:36:37,043 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-0231FCONFIANZA-20250613013635.xls
2025-06-13 06:36:37,137 - root - INFO - Archivo: RETIROS-0231FCONFIANZA-20250613013635.xls.signature subido a: s3://prd-datalake-reports-637423440311/0231FCONFIANZA/2025-06-13/RETIROS/RETIROS-0231FCONFIANZA-20250613013635.xls.signature
2025-06-13 06:36:37,237 - root - INFO - Archivo: RETIROS-0231FCONFIANZA-20250613013635.xls subido a: s3://prd-datalake-reports-637423440311/0231FCONFIANZA/2025-06-13/RETIROS/RETIROS-0231FCONFIANZA-20250613013635.xls
2025-06-13 06:36:37,313 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:37,346 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:37,346 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250613013635.xls
2025-06-13 06:36:37,347 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250613013635.xls.signature
2025-06-13 06:36:37,347 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250613013635.xls with signature: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250613013635.xls.signature
2025-06-13 06:36:37,347 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-13 06:36:37,347 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250613013635.xls
2025-06-13 06:36:37,421 - root - INFO - Archivo: RETIROS-FQAPAQ-20250613013635.xls.signature subido a: s3://prd-datalake-reports-637423440311/FQAPAQ/2025-06-13/RETIROS/RETIROS-FQAPAQ-20250613013635.xls.signature
2025-06-13 06:36:37,502 - root - INFO - Archivo: RETIROS-FQAPAQ-20250613013635.xls subido a: s3://prd-datalake-reports-637423440311/FQAPAQ/2025-06-13/RETIROS/RETIROS-FQAPAQ-20250613013635.xls
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='FCONFIANZA'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='FQAPAQ'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
Conexión a Oracle establecida.
/home/<USER>/generate/exports_excel/export_to_excel.py:28: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, con=connection)
2025-06-13 06:36:37,574 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:37,607 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-13 06:36:37,607 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250613013635.xls
2025-06-13 06:36:37,609 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250613013635.xls.signature
2025-06-13 06:36:37,609 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250613013635.xls with signature: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250613013635.xls.signature
2025-06-13 06:36:37,609 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-13 06:36:37,609 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250613013635.xls
2025-06-13 06:36:37,687 - root - INFO - Archivo: RETIROS-FQAPAQ-20250613013635.xls.signature subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-06-13/RETIROS/RETIROS-FQAPAQ-20250613013635.xls.signature
2025-06-13 06:36:37,790 - root - INFO - Archivo: RETIROS-FQAPAQ-20250613013635.xls subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-06-13/RETIROS/RETIROS-FQAPAQ-20250613013635.xls
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
