/home/<USER>/generate/exports_excel/export_to_excel.py:28: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, con=connection)
2025-06-13 06:35:06,005 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-13 06:35:06,154 - root - INFO - Archivo: PDP-REPORTE-BITELPRE-20250613000000.xls subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-06-13/BITEL-PRE/PDP-REPORTE-BITELPRE-20250613000000.xls
Conexión a Oracle establecida.
select
'BIM' as partner,
'00000' as process_code,
--payee_identifier_value,
Field7 as trace,
--SUBSTR(FIELD8,1,INSTR(FIELD8,'_')-1) AS ISDN,
Remarks as ISDN,
--Transfer_value as monto,
LPAD(SUBSTR(TO_CHAR(TRANSFER_VALUE), 1, LENGTH(TO_CHAR(TRANSFER_VALUE))-2), LENGTH(TO_CHAR(TRANSFER_VALUE))-2, '0') || '.' || SUBSTR(TO_CHAR(TRANSFER_VALUE), LENGTH(TO_CHAR(TRANSFER_VALUE))-1, 2) AS monto,
Case when transfer_status = 'TS' then '00' else NULL end as respuesta,
Case when transfer_status = 'TS' then transfer_date else NULL end as fecha
From PDP_PROD10_MAINDBBUS.mtx_transaction_header
Where SERVICE_TYPE = 'BILLPAY'
AND payee_identifier_value like 'airtimebitel%'
And trunc(transfer_date) = trunc(to_date('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))

Conexión cerrada.
