/home/<USER>/generate/exports_excel/export_to_excel.py:28: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, con=connection)
2025-06-13 06:36:12,929 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-13 06:36:13,110 - root - INFO - Archivo: Reporte de Servicios Directos-2025.xlsx subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-06-13/SERVICIOS-DIRECTOS/Reporte de Servicios Directos-2025.xlsx
Conexión a Oracle establecida.
 ---SERVICIOS DIRECTOS 28-03-2025 MODIF
SELECT
TOTAL."Anio_mes",
TOTAL."Dia de Operacion",
TOTAL."ID Origen",
TOTAL."Perfil Origen",
TOTAL."Ce<PERSON>lar Bimer",
TOTA<PERSON>."ID Destino",
TOTAL."Perfil Destino",
<PERSON>UM(TOTAL."Amount") AS "Total Monto",
COUNT(TOTAL."TransferID") AS "Cant. Trx",
TOTAL.REMARKS AS "complete_to_username",
TOTAL."Tipo",
TOTAL."Estado"
FROM(
	SELECT
	TO_CHAR(PLT."TransferDate", 'YYYY_MM') AS "Anio_mes",
	TO_CHAR(PLT."TransferDate", 'DD/MM/YYYY') AS "Dia de Operacion",
	PLT."FromID" AS "ID Origen", --BTR.PAYER_USER_ID               
	PLT."From_Profile" AS "Perfil Origen",
	PLT."To_Msisdn" as "Celular Bimer",
	PLT."ToID" AS "ID Destino",
	PLT."To_Profile" AS "Perfil Destino",
	PLT."TransferStatus" AS "Estado",  --STATUS  
	PLT."Remarks" ||'@'||PLT."To_Identifier" AS REMARKS,
	PLT."TransferID", --TRANSFER_ID
	PLT."Amount"/100 AS "Amount",  --TRANSFER_VALUE
	CASE
	WHEN PLT."To_Identifier" LIKE 'bitel%' THEN 'Post Pago Bitel'
	WHEN PLT."To_Identifier" LIKE 'sunat%' THEN 'SUNAT'
	WHEN PLT."To_Identifier" LIKE 'unique%' THEN 'Unique'
	WHEN PLT."To_Identifier" LIKE 'lindley%' THEN 'Recargas Azulito' --cambiar wu por lindley
	WHEN PLT."To_Identifier" LIKE 'claro%' THEN 'Post Pago Claro'
	END as "Tipo"
	FROM USR_DATALAKE.PRE_LOG_TRX PLT
	LEFT JOIN (
	SELECT M.RECONCILIATION_BY AS TRANSFER_ID
	FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER M
	WHERE M.SERVICE_TYPE = 'TXNCORRECT'
	AND TO_CHAR(M.TRANSFER_DATE, 'YYYY') = TO_CHAR(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'), 'YYYY') ---2025-06-12 00:00:00
	AND TRUNC(M.TRANSFER_DATE) <= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
	AND TRANSFER_STATUS = 'TS') REV
ON PLT."TransferID_Mob" = REV.TRANSFER_ID 
WHERE
PLT."From_Workspace" = 'SUBSCRIBER'
AND REPLACE(PLT."To_Identifier",'1','') IN ('bitel','sunat','unique','wu','claro') --cambiar wu por lindley
AND PLT."TransactionType" = 'EXTERNAL_PAYMENT'
AND TO_CHAR(PLT."TransferDate", 'YYYY') = TO_CHAR(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'), 'YYYY') ---2025-06-12 00:00:00
AND TRUNC(PLT."TransferDate") <= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
) TOTAL
GROUP BY
TOTAL."Celular Bimer",
TOTAL."Estado",
TOTAL."Perfil Origen",
TOTAL."Dia de Operacion",
TOTAL."Anio_mes",
TOTAL."Tipo",
TOTAL."ID Origen",
TOTAL."ID Destino",
TOTAL."Perfil Destino",
TOTAL.REMARKS

Conexión cerrada.
