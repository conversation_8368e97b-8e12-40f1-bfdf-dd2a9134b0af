2025-06-12 14:54:40,241 - root - INFO - No se encontraron datos para exportar.
2025-06-12 14:54:40,244 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-FCOMPARTAMOS-COBRAR-202506-PDF.csv
2025-06-12 14:54:40,338 - root - INFO - No se encontraron datos para exportar.
2025-06-12 14:54:40,339 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-BNACION-COBRAR-202506-PDF.csv

WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCOMPARTAMOS' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'FCOMPARTAMOS' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCOMPARTAMOS' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'FCOMPARTAMOS' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Error al ejecutar la consulta en Oracle: ORA-00900: invalid SQL statement
Help: https://docs.oracle.com/error-help/db/ora-00900/
[]

WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'BNACION' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'BNACION' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'BNACION' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'BNACION' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Error al ejecutar la consulta en Oracle: ORA-00900: invalid SQL statement
Help: https://docs.oracle.com/error-help/db/ora-00900/
[]
2025-06-12 14:54:40,527 - root - INFO - No se encontraron datos para exportar.
2025-06-12 14:54:40,528 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-CRANDES-COBRAR-202506-PDF.csv
2025-06-12 14:54:40,712 - root - INFO - No se encontraron datos para exportar.
2025-06-12 14:54:40,713 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-CCUSCO-COBRAR-202506-PDF.csv

WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CRANDES' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'CRANDES' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CRANDES' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'CRANDES' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Error al ejecutar la consulta en Oracle: ORA-00900: invalid SQL statement
Help: https://docs.oracle.com/error-help/db/ora-00900/
[]

WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CCUSCO' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'CCUSCO' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CCUSCO' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'CCUSCO' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Error al ejecutar la consulta en Oracle: ORA-00900: invalid SQL statement
Help: https://docs.oracle.com/error-help/db/ora-00900/
[]
2025-06-12 14:54:40,962 - root - INFO - No se encontraron datos para exportar.
2025-06-12 14:54:40,963 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-0231FCONFIANZA-COBRAR-202506-PDF.csv
2025-06-12 14:54:41,104 - root - INFO - No se encontraron datos para exportar.
2025-06-12 14:54:41,105 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-FQAPAQ-COBRAR-202506-PDF.csv

WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCONFIANZA' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'FCONFIANZA' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCONFIANZA' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'FCONFIANZA' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Error al ejecutar la consulta en Oracle: ORA-00900: invalid SQL statement
Help: https://docs.oracle.com/error-help/db/ora-00900/
[]

WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FQAPAQ' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'FQAPAQ' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END  AS "Agente",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FQAPAQ' AND PLT."From_Workspace" = 'BUSINESS') --CAMBIAR POR EMISOR
	OR (TLTX."To_BankDomain" = 'FQAPAQ' AND PLT."To_Workspace" = 'BUSINESS')) --CAMBIAR POR EMISOR
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Error al ejecutar la consulta en Oracle: ORA-00900: invalid SQL statement
Help: https://docs.oracle.com/error-help/db/ora-00900/
[]
