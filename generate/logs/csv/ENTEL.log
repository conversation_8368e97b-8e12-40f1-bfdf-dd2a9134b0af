2025-06-13 06:41:33,202 - root - INFO - <PERSON><PERSON> encontrados. Procediendo a exportar el archivo CSV.
2025-06-13 06:41:33,205 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/PDP-REPORTE-ENTELTDE-20250613.csv
2025-06-13 06:41:33,217 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-13 06:41:33,358 - root - INFO - Archivo: PDP-REPORTE-ENTELTDE-20250613.csv subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-06-13/ENTEL/PDP-REPORTE-ENTELTDE-20250613.csv
WITH
DATA_ENTEL AS (
	SELECT
	"TransferID" AS trx_id,
	"TransferID" AS financial_trx_id,
	"ExternalTransactionID" AS external_trx_id,
	TO_CHAR("TransferDate", 'YYYY-MM-DD HH24:MI:SS') as datetime,
	"From_Msisdn" AS msisdn,
	"Remarks" ||'@'||REPLACE("To_Identifier",'1','') AS complete_username,
	LPAD(SUBSTR(TO_CHAR("Amount"), 1, LENGTH(TO_CHAR("Amount"))-2), LENGTH(TO_CHAR("Amount"))-2, '0') || '.' || SUBSTR(TO_CHAR("Amount"), LENGTH(TO_CHAR("Amount"))-1, 2) AS monto,
	"Amount" AS monto_real
	FROM USR_DATALAKE.PRE_LOG_TRX
	WHERE "TransactionType" = 'EXTERNAL_PAYMENT'
	AND "To_Identifier" LIKE '%airtimeentel%'
	AND TRUNC("TransferDate") = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
)
SELECT
TRX_ID || ',' ||
FINANCIAL_TRX_ID || ',' ||
EXTERNAL_TRX_ID  || ',' ||
DATETIME  || ',' ||
MSISDN  || ',' ||
COMPLETE_USERNAME  || ',' ||
MONTO AS SALIDA
FROM DATA_ENTEL
UNION ALL
SELECT
'TOTAL ENTEL, ' ||
LPAD(SUBSTR(TO_CHAR(SUM(MONTO_REAL)), 1, LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2, '0') || '.' || SUBSTR(TO_CHAR(SUM(MONTO_REAL)), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-1, 2)
FROM DATA_ENTEL

Ejecutando query: WITH
DATA_ENTEL AS (
	SELECT
	"TransferID" AS trx_id,
	"TransferID" AS financial_trx_id,
	"ExternalTransactionID" AS external_trx_id,
	TO_CHAR("TransferDate", 'YYYY-MM-DD HH24:MI:SS') as datetime,
	"From_Msisdn" AS msisdn,
	"Remarks" ||'@'||REPLACE("To_Identifier",'1','') AS complete_username,
	LPAD(SUBSTR(TO_CHAR("Amount"), 1, LENGTH(TO_CHAR("Amount"))-2), LENGTH(TO_CHAR("Amount"))-2, '0') || '.' || SUBSTR(TO_CHAR("Amount"), LENGTH(TO_CHAR("Amount"))-1, 2) AS monto,
	"Amount" AS monto_real
	FROM USR_DATALAKE.PRE_LOG_TRX
	WHERE "TransactionType" = 'EXTERNAL_PAYMENT'
	AND "To_Identifier" LIKE '%airtimeentel%'
	AND TRUNC("TransferDate") = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
)
SELECT
TRX_ID || ',' ||
FINANCIAL_TRX_ID || ',' ||
EXTERNAL_TRX_ID  || ',' ||
DATETIME  || ',' ||
MSISDN  || ',' ||
COMPLETE_USERNAME  || ',' ||
MONTO AS SALIDA
FROM DATA_ENTEL
UNION ALL
SELECT
'TOTAL ENTEL, ' ||
LPAD(SUBSTR(TO_CHAR(SUM(MONTO_REAL)), 1, LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2, '0') || '.' || SUBSTR(TO_CHAR(SUM(MONTO_REAL)), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-1, 2)
FROM DATA_ENTEL

[<src.core.models.report_row.ReportRow object at 0x7f16f9d203a0>]
Archivo guardado correctamente en /home/<USER>/output/csv/PDP-REPORTE-ENTELTDE-20250613.csv
