2025-06-13 06:40:38,575 - root - INFO - No se encontraron datos para exportar.
2025-06-13 06:40:38,577 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/PDP-EQUIVALENCIA-AHORROS-20250613.csv
2025-06-13 06:40:38,589 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-13 06:40:38,730 - root - INFO - Archivo: PDP-EQUIVALENCIA-AHORROS-20250613.csv subido a: s3://prd-datalake-reports-637423440311/FCOMPARTAMOS/2025-06-13/EQUIVALENCIA-AHORROS/PDP-EQUIVALENCIA-AHORROS-20250613.csv
SELECT
P."TransferID_Mob" AS ID,
H."FinancialTransactionID" AS ID_NUEVO,
TO_CHAR(P."TransferDate",'DD/MM/YYYY HH24:MI') AS FECHA,
CASE WHEN H."TransactionStatus" = 'COMMITTED' THEN 'Exitoso'
ELSE 'Fallido' END AS ESTADO,
CASE WHEN H."FromMSISDN" = '51985020019' THEN 'Transferencia'
ELSE 'Pago a comercio' END AS TIPO,
'' AS INFORMACION,
CASE WHEN H."FromMSISDN" = '51985020019' THEN 'Reversa ahorro compartamos'
ELSE '' END AS MENSAJE,
CASE WHEN H."FromMSISDN" = '51985020019' THEN REV."TransferID"
ELSE '' END AS Id_REVERSA,
CASE WHEN H."FromMSISDN" = '51985020019' THEN P."ReversalID"
ELSE '' END AS Id_REVERSA_PDP,
'FRI:' || H."FromMSISDN" || '/MSISDN' AS DE,
'FRI:' || H."ToMSISDN" || '/MSISDN' AS PARA,
CASE WHEN H."FromMSISDN" = '51985020019' THEN H."InitiatingUser"
ELSE '' END AS INICIADO_POR,
CASE WHEN H."FromMSISDN" = '51985020019' THEN H."RealUser"
ELSE '' END AS EN_NOMBRE_DE,
CASE WHEN H."FromMSISDN" = '51985020019' THEN  -H."Amount"
ELSE H."Amount" END AS MONTO
FROM USR_DATALAKE.LOG_TRX_FINAL H
LEFT JOIN USR_DATALAKE.PRE_LOG_TRX P ON H."FinancialTransactionID" = P."TransferID"
LEFT JOIN USR_DATALAKE.PRE_LOG_TRX REV ON P."ReversalID" = REV."TransferID_Mob"
WHERE (H."FromMSISDN" = '51985020019' OR H."ToMSISDN" = '51985020019')
AND H."TransactionType" IN ('TRANSFER','PAYMENT')
AND TRUNC(TO_DATE(H."DateTime", 'YYYY-MM-DD HH24:MI:SS')) >= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
ORDER BY P."TransferDate" DESC

Ejecutando query: SELECT
P."TransferID_Mob" AS ID,
H."FinancialTransactionID" AS ID_NUEVO,
TO_CHAR(P."TransferDate",'DD/MM/YYYY HH24:MI') AS FECHA,
CASE WHEN H."TransactionStatus" = 'COMMITTED' THEN 'Exitoso'
ELSE 'Fallido' END AS ESTADO,
CASE WHEN H."FromMSISDN" = '51985020019' THEN 'Transferencia'
ELSE 'Pago a comercio' END AS TIPO,
'' AS INFORMACION,
CASE WHEN H."FromMSISDN" = '51985020019' THEN 'Reversa ahorro compartamos'
ELSE '' END AS MENSAJE,
CASE WHEN H."FromMSISDN" = '51985020019' THEN REV."TransferID"
ELSE '' END AS Id_REVERSA,
CASE WHEN H."FromMSISDN" = '51985020019' THEN P."ReversalID"
ELSE '' END AS Id_REVERSA_PDP,
'FRI:' || H."FromMSISDN" || '/MSISDN' AS DE,
'FRI:' || H."ToMSISDN" || '/MSISDN' AS PARA,
CASE WHEN H."FromMSISDN" = '51985020019' THEN H."InitiatingUser"
ELSE '' END AS INICIADO_POR,
CASE WHEN H."FromMSISDN" = '51985020019' THEN H."RealUser"
ELSE '' END AS EN_NOMBRE_DE,
CASE WHEN H."FromMSISDN" = '51985020019' THEN  -H."Amount"
ELSE H."Amount" END AS MONTO
FROM USR_DATALAKE.LOG_TRX_FINAL H
LEFT JOIN USR_DATALAKE.PRE_LOG_TRX P ON H."FinancialTransactionID" = P."TransferID"
LEFT JOIN USR_DATALAKE.PRE_LOG_TRX REV ON P."ReversalID" = REV."TransferID_Mob"
WHERE (H."FromMSISDN" = '51985020019' OR H."ToMSISDN" = '51985020019')
AND H."TransactionType" IN ('TRANSFER','PAYMENT')
AND TRUNC(TO_DATE(H."DateTime", 'YYYY-MM-DD HH24:MI:SS')) >= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
ORDER BY P."TransferDate" DESC

No se encontraron registros en la consulta.
[]
