2025-06-13 06:43:40,956 - root - INFO - No se encontraron datos para exportar.
2025-06-13 06:43:40,958 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/PDP-REPORTE-BACKUS-20250613.csv
2025-06-13 06:43:40,970 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-13 06:43:41,123 - root - INFO - Archivo: PDP-REPORTE-BACKUS-20250613.csv subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-06-13/QR-NIUBIZ/PDP-REPORTE-BACKUS-20250613.csv
SELECT
H."TransferID" as id,
H."TransferDate" as fecha,
H."TranscationType" as tipo,
H."From_Msisdn" as de,
H."To_Msisdn" as para,
H."Amount" / 100 as monto,
0 AS cargo,
F."Context" AS context,
F."FromUsername"  AS complete_to_username,
'COMMITED' AS transaction_status
FROM USR_DATALAKE.PRE_LOG_TRX H
INNER JOIN USR_DATALAKE.LOG_TRX_FINAL F
ON "TransferID" = "FinancialTransactionID"
WHERE "From_Msisdn" = '51989320150'
AND TO_CHAR("TransferDate",'YYYY-MM') = TO_CHAR(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YYYY-MM')
UNION
select
H."TransferID"  as id,
H."TransferDate" as fecha,
H."TranscationType"  as tipo,
H."From_Msisdn" as de,
H."To_Msisdn" as para,
H."Amount" / 100 as monto,
H."Fee"/100 AS cargo,
F."Context" AS context,
F."ToUsername"  AS complete_to_username,
'COMMITED' AS transaction_status
FROM USR_DATALAKE.PRE_LOG_TRX H
INNER JOIN USR_DATALAKE.LOG_TRX_FINAL F
ON "TransferID" = "FinancialTransactionID"
where
"To_Msisdn" = '51989320150'
AND TO_CHAR("TransferDate",'YYYY-MM') = TO_CHAR(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YYYY-MM')

Ejecutando query: SELECT
H."TransferID" as id,
H."TransferDate" as fecha,
H."TranscationType" as tipo,
H."From_Msisdn" as de,
H."To_Msisdn" as para,
H."Amount" / 100 as monto,
0 AS cargo,
F."Context" AS context,
F."FromUsername"  AS complete_to_username,
'COMMITED' AS transaction_status
FROM USR_DATALAKE.PRE_LOG_TRX H
INNER JOIN USR_DATALAKE.LOG_TRX_FINAL F
ON "TransferID" = "FinancialTransactionID"
WHERE "From_Msisdn" = '51989320150'
AND TO_CHAR("TransferDate",'YYYY-MM') = TO_CHAR(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YYYY-MM')
UNION
select
H."TransferID"  as id,
H."TransferDate" as fecha,
H."TranscationType"  as tipo,
H."From_Msisdn" as de,
H."To_Msisdn" as para,
H."Amount" / 100 as monto,
H."Fee"/100 AS cargo,
F."Context" AS context,
F."ToUsername"  AS complete_to_username,
'COMMITED' AS transaction_status
FROM USR_DATALAKE.PRE_LOG_TRX H
INNER JOIN USR_DATALAKE.LOG_TRX_FINAL F
ON "TransferID" = "FinancialTransactionID"
where
"To_Msisdn" = '51989320150'
AND TO_CHAR("TransferDate",'YYYY-MM') = TO_CHAR(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YYYY-MM')

Error al ejecutar la consulta en Oracle: ORA-00904: "H"."TranscationType": invalid identifier
Help: https://docs.oracle.com/error-help/db/ora-00904/
[]
