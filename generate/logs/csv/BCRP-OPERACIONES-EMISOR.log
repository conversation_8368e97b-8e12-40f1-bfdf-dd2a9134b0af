WITH
PRE_DATA AS (
SELECT
"From_BankDomain" AS Origen,
"To_BankDomain" AS Destino,
"TransactionType" AS Tipo,
"Amount" AS Monto,
--CASE WHEN "TransactionType" = 'CASH_IN' AND "From_BankDomain" <> "To_BankDomain" THEN 1 ELSE 0 END AS N_CASHIN,
--CASE WHEN "TransactionType" = 'CASH_IN' AND "From_BankDomain" <> "To_BankDomain"THEN "Amount" ELSE 0 END AS M_CASHIN,
CASE WHEN "TransactionType" = 'TRANSFER' AND "From_BankDomain" <> "To_BankDomain" THEN 1 ELSE 0 END AS N_TRANS_ENV,
CASE WHEN "TransactionType" = 'TRANSFER' AND "From_BankDomain" <> "To_BankDomain" THEN "Amount" ELSE 0 END AS M_TRANS_ENV,
CASE WHEN "TransactionType" = 'TRANSFER' AND "From_BankDomain" = "To_BankDomain" THEN 1 ELSE 0 END AS N_TRANS_INTER,
CASE WHEN "TransactionType" = 'TRANSFER' AND "From_BankDomain" = "To_BankDomain" THEN "Amount" ELSE 0 END AS M_TRANS_INTER,
CASE WHEN "TransactionType" IN ('CASH_OUT','CASH_OUT_ATM') AND "From_BankDomain" <> "To_BankDomain" THEN 1 ELSE 0 END AS N_CASHOUT,
CASE WHEN "TransactionType" IN ('CASH_OUT','CASH_OUT_ATM') AND "From_BankDomain" <> "To_BankDomain" THEN "Amount" ELSE 0 END AS M_CASHOUT,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') LIKE 'airtime%' THEN 1 ELSE 0 END AS N_RECARGAS,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') LIKE 'airtime%' THEN "Amount" ELSE 0 END AS M_RECARGAS,
CASE WHEN "TransactionType" = 'PAYMENT' THEN 1 ELSE 0 END AS N_PAGO_COMERCIO,
CASE WHEN "TransactionType" = 'PAYMENT' THEN "Amount" ELSE 0 END AS M_PAGO_COMERCIO,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') IN ('backus','lindley','gloria') THEN 1 ELSE 0 END AS N_PAGO_PROVEEDORES,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') IN ('backus','lindley','gloria') THEN "Amount" ELSE 0 END AS M_PAGO_PROVEEDORES,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') IN ('tecsup','sunat','promujer','bitel') THEN 1 ELSE 0 END AS N_PAGO_SERVICIOS,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') IN ('tecsup','sunat','promujer','bitel') THEN "Amount" ELSE 0 END AS M_PAGO_SERVICIOS,
CASE WHEN "TransactionType" IN ('TRANSFER_FROM_INVITATION') THEN 1 ELSE 0 END AS N_OTROS,
CASE WHEN "TransactionType" IN ('TRANSFER_FROM_INVITATION') THEN "Amount" ELSE 0 END AS M_OTROS,
"Currency" AS MONEDA
FROM USR_DATALAKE.LOG_TRX_FINAL
WHERE TRUNC(TO_DATE("DateTime",'YYYY-MM-DD HH24:MI:SS')) = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
),
DATA_FINAL AS (
	SELECT
	ORIGEN AS Emisor,
	--SUM(N_CASHIN) AS Num_Cash_In,
	--SUM(M_CASHIN) AS Monto_Cash_In,
	COALESCE((SELECT COUNT(1) FROM PRE_DATA A2 WHERE A2.DESTINO = P.ORIGEN AND TIPO = 'CASH_IN' AND A2.DESTINO <> A2.ORIGEN),0) AS Num_Cash_In,
	COALESCE((SELECT SUM(MONTO) FROM PRE_DATA A2 WHERE A2.DESTINO = P.ORIGEN AND TIPO = 'CASH_IN' AND A2.DESTINO <> A2.ORIGEN),0) AS Monto_Cash_In,
	COALESCE((SELECT COUNT(1) FROM PRE_DATA A2 WHERE A2.DESTINO = P.ORIGEN AND TIPO = 'TRANSFER' AND A2.DESTINO <> A2.ORIGEN),0) AS Num_Trans_Rec,
	COALESCE((SELECT SUM(MONTO) FROM PRE_DATA A2 WHERE A2.DESTINO = P.ORIGEN AND TIPO = 'TRANSFER' AND A2.DESTINO <> A2.ORIGEN),0) AS Monto_Trans_Rec,
	SUM(N_TRANS_ENV) AS Num_Trans_Env,
	SUM(M_TRANS_ENV) AS Monto_Trans_Env,
	SUM(N_TRANS_INTER) AS Num_Trans_Intra,
	SUM(M_TRANS_INTER) AS Monto_Trans_Intra,
	SUM(N_CASHOUT) AS Num_Cash_Out,
	SUM(M_CASHOUT) AS Monto_Cash_Out,
	SUM(N_RECARGAS) AS Num_Recarga,
	SUM(M_RECARGAS) AS Monto_Recarga,
	SUM(N_PAGO_COMERCIO) AS Num_Pago_Comercio,
	SUM(M_PAGO_COMERCIO) AS Monto_Pago_Comercio,
	SUM(N_PAGO_PROVEEDORES) AS Num_Pago_Prov,
	SUM(M_PAGO_PROVEEDORES) AS Monto_Pago_Prov,
	SUM(N_PAGO_SERVICIOS) AS Num_Pago_Servicio,
	SUM(M_PAGO_SERVICIOS) AS Monto_Pago_Servicio,
	SUM(N_OTROS) AS Num_Otro,
	SUM(M_OTROS) AS Monto_Otro,
	MONEDA
	FROM PRE_DATA P 
	GROUP BY ORIGEN, MONEDA
)
SELECT
ID.ISSUER_CODE AS EMISOR,
COALESCE(NUM_CASH_IN,0) AS NUM_CASH_IN,
COALESCE(MONTO_CASH_IN,0) AS MONTO_CASH_IN,
COALESCE(NUM_TRANS_REC,0) AS NUM_TRANS_REC,
COALESCE(MONTO_TRANS_REC,0) AS MONTO_TRANS_REC,
COALESCE(NUM_TRANS_ENV,0) AS NUM_TRANS_ENV,
COALESCE(MONTO_TRANS_ENV,0) AS MONTO_TRANS_ENV, 
COALESCE(NUM_TRANS_INTRA,0) AS NUM_TRANS_INTRA, 
COALESCE(MONTO_TRANS_INTRA,0) AS MONTO_TRANS_INTRA,
COALESCE(NUM_CASH_OUT,0) AS NUM_CASH_OUT,
COALESCE(MONTO_CASH_OUT,0) AS MONTO_CASH_OUT, 
COALESCE(NUM_RECARGA,0) AS NUM_RECARGA,
COALESCE(MONTO_RECARGA,0) AS MONTO_RECARGA,
COALESCE(NUM_PAGO_COMERCIO,0) AS NUM_PAGO_COMERCIO,
COALESCE(MONTO_PAGO_COMERCIO,0) AS MONTO_PAGO_COMERCIO,
COALESCE(NUM_PAGO_PROV,0) AS NUM_PAGO_PROV, 
COALESCE(MONTO_PAGO_PROV,0) AS MONTO_PAGO_PROV,
COALESCE(NUM_PAGO_SERVICIO,0) AS NUM_PAGO_SERVICIO,
COALESCE(MONTO_PAGO_SERVICIO,0) AS MONTO_PAGO_SERVICIO, 
COALESCE(NUM_OTRO,0) AS NUM_OTRO,
COALESCE(MONTO_OTRO,0) AS MONTO_OTRO, 
COALESCE(MONEDA,'PEN') AS MONEDA
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID 
LEFT JOIN DATA_FINAL DF ON ID.ISSUER_CODE = DF.EMISOR
UNION ALL
SELECT 
'TOTAL DIARIO' AS EMISOR,
SUM(NUM_CASH_IN) AS NUM_CASH_IN,
SUM(MONTO_CASH_IN) AS MONTO_CASH_IN,
SUM(NUM_TRANS_REC) AS NUM_TRANS_REC,
SUM(MONTO_TRANS_REC) AS MONTO_TRANS_REC,
SUM(NUM_TRANS_ENV) AS NUM_TRANS_ENV,
SUM(MONTO_TRANS_ENV) AS MONTO_TRANS_ENV, 
SUM(NUM_TRANS_INTRA) AS NUM_TRANS_INTRA, 
SUM(MONTO_TRANS_INTRA) AS MONTO_TRANS_INTRA,
SUM(NUM_CASH_OUT) AS NUM_CASH_OUT,
SUM(MONTO_CASH_OUT) AS MONTO_CASH_OUT, 
SUM(NUM_RECARGA) AS NUM_RECARGA,
SUM(MONTO_RECARGA) AS MONTO_RECARGA,
SUM(NUM_PAGO_COMERCIO) AS NUM_PAGO_COMERCIO,
SUM(MONTO_PAGO_COMERCIO) AS MONTO_PAGO_COMERCIO,
SUM(NUM_PAGO_PROV) AS NUM_PAGO_PROV, 
SUM(MONTO_PAGO_PROV) AS MONTO_PAGO_PROV,
SUM(NUM_PAGO_SERVICIO) AS NUM_PAGO_SERVICIO,
SUM(MONTO_PAGO_SERVICIO) AS MONTO_PAGO_SERVICIO,
SUM(NUM_OTRO) AS NUM_OTRO,
SUM(MONTO_OTRO) AS MONTO_OTRO,
'PEN' AS MONEDA
FROM DATA_FINAL

2025-06-13 06:40:58,892 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-06-13 06:40:58,896 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/BCRP-OPERACIONES_POR_EMISOR-**************.csv
2025-06-13 06:40:58,908 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-13 06:40:59,080 - root - INFO - Archivo: BCRP-OPERACIONES_POR_EMISOR-**************.csv subido a: s3://prd-datalake-reports-************/BCRP/2025-06-13/BCRP-OPERACIONES-EMISOR/BCRP-OPERACIONES_POR_EMISOR-**************.csv
Ejecutando query: WITH
PRE_DATA AS (
SELECT
"From_BankDomain" AS Origen,
"To_BankDomain" AS Destino,
"TransactionType" AS Tipo,
"Amount" AS Monto,
--CASE WHEN "TransactionType" = 'CASH_IN' AND "From_BankDomain" <> "To_BankDomain" THEN 1 ELSE 0 END AS N_CASHIN,
--CASE WHEN "TransactionType" = 'CASH_IN' AND "From_BankDomain" <> "To_BankDomain"THEN "Amount" ELSE 0 END AS M_CASHIN,
CASE WHEN "TransactionType" = 'TRANSFER' AND "From_BankDomain" <> "To_BankDomain" THEN 1 ELSE 0 END AS N_TRANS_ENV,
CASE WHEN "TransactionType" = 'TRANSFER' AND "From_BankDomain" <> "To_BankDomain" THEN "Amount" ELSE 0 END AS M_TRANS_ENV,
CASE WHEN "TransactionType" = 'TRANSFER' AND "From_BankDomain" = "To_BankDomain" THEN 1 ELSE 0 END AS N_TRANS_INTER,
CASE WHEN "TransactionType" = 'TRANSFER' AND "From_BankDomain" = "To_BankDomain" THEN "Amount" ELSE 0 END AS M_TRANS_INTER,
CASE WHEN "TransactionType" IN ('CASH_OUT','CASH_OUT_ATM') AND "From_BankDomain" <> "To_BankDomain" THEN 1 ELSE 0 END AS N_CASHOUT,
CASE WHEN "TransactionType" IN ('CASH_OUT','CASH_OUT_ATM') AND "From_BankDomain" <> "To_BankDomain" THEN "Amount" ELSE 0 END AS M_CASHOUT,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') LIKE 'airtime%' THEN 1 ELSE 0 END AS N_RECARGAS,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') LIKE 'airtime%' THEN "Amount" ELSE 0 END AS M_RECARGAS,
CASE WHEN "TransactionType" = 'PAYMENT' THEN 1 ELSE 0 END AS N_PAGO_COMERCIO,
CASE WHEN "TransactionType" = 'PAYMENT' THEN "Amount" ELSE 0 END AS M_PAGO_COMERCIO,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') IN ('backus','lindley','gloria') THEN 1 ELSE 0 END AS N_PAGO_PROVEEDORES,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') IN ('backus','lindley','gloria') THEN "Amount" ELSE 0 END AS M_PAGO_PROVEEDORES,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') IN ('tecsup','sunat','promujer','bitel') THEN 1 ELSE 0 END AS N_PAGO_SERVICIOS,
CASE WHEN "TransactionType" = 'EXTERNAL_PAYMENT' AND REPLACE(SUBSTR("ToUsername",INSTR("ToUsername",'@')+1),'1','') IN ('tecsup','sunat','promujer','bitel') THEN "Amount" ELSE 0 END AS M_PAGO_SERVICIOS,
CASE WHEN "TransactionType" IN ('TRANSFER_FROM_INVITATION') THEN 1 ELSE 0 END AS N_OTROS,
CASE WHEN "TransactionType" IN ('TRANSFER_FROM_INVITATION') THEN "Amount" ELSE 0 END AS M_OTROS,
"Currency" AS MONEDA
FROM USR_DATALAKE.LOG_TRX_FINAL
WHERE TRUNC(TO_DATE("DateTime",'YYYY-MM-DD HH24:MI:SS')) = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
),
DATA_FINAL AS (
	SELECT
	ORIGEN AS Emisor,
	--SUM(N_CASHIN) AS Num_Cash_In,
	--SUM(M_CASHIN) AS Monto_Cash_In,
	COALESCE((SELECT COUNT(1) FROM PRE_DATA A2 WHERE A2.DESTINO = P.ORIGEN AND TIPO = 'CASH_IN' AND A2.DESTINO <> A2.ORIGEN),0) AS Num_Cash_In,
	COALESCE((SELECT SUM(MONTO) FROM PRE_DATA A2 WHERE A2.DESTINO = P.ORIGEN AND TIPO = 'CASH_IN' AND A2.DESTINO <> A2.ORIGEN),0) AS Monto_Cash_In,
	COALESCE((SELECT COUNT(1) FROM PRE_DATA A2 WHERE A2.DESTINO = P.ORIGEN AND TIPO = 'TRANSFER' AND A2.DESTINO <> A2.ORIGEN),0) AS Num_Trans_Rec,
	COALESCE((SELECT SUM(MONTO) FROM PRE_DATA A2 WHERE A2.DESTINO = P.ORIGEN AND TIPO = 'TRANSFER' AND A2.DESTINO <> A2.ORIGEN),0) AS Monto_Trans_Rec,
	SUM(N_TRANS_ENV) AS Num_Trans_Env,
	SUM(M_TRANS_ENV) AS Monto_Trans_Env,
	SUM(N_TRANS_INTER) AS Num_Trans_Intra,
	SUM(M_TRANS_INTER) AS Monto_Trans_Intra,
	SUM(N_CASHOUT) AS Num_Cash_Out,
	SUM(M_CASHOUT) AS Monto_Cash_Out,
	SUM(N_RECARGAS) AS Num_Recarga,
	SUM(M_RECARGAS) AS Monto_Recarga,
	SUM(N_PAGO_COMERCIO) AS Num_Pago_Comercio,
	SUM(M_PAGO_COMERCIO) AS Monto_Pago_Comercio,
	SUM(N_PAGO_PROVEEDORES) AS Num_Pago_Prov,
	SUM(M_PAGO_PROVEEDORES) AS Monto_Pago_Prov,
	SUM(N_PAGO_SERVICIOS) AS Num_Pago_Servicio,
	SUM(M_PAGO_SERVICIOS) AS Monto_Pago_Servicio,
	SUM(N_OTROS) AS Num_Otro,
	SUM(M_OTROS) AS Monto_Otro,
	MONEDA
	FROM PRE_DATA P 
	GROUP BY ORIGEN, MONEDA
)
SELECT
ID.ISSUER_CODE AS EMISOR,
COALESCE(NUM_CASH_IN,0) AS NUM_CASH_IN,
COALESCE(MONTO_CASH_IN,0) AS MONTO_CASH_IN,
COALESCE(NUM_TRANS_REC,0) AS NUM_TRANS_REC,
COALESCE(MONTO_TRANS_REC,0) AS MONTO_TRANS_REC,
COALESCE(NUM_TRANS_ENV,0) AS NUM_TRANS_ENV,
COALESCE(MONTO_TRANS_ENV,0) AS MONTO_TRANS_ENV, 
COALESCE(NUM_TRANS_INTRA,0) AS NUM_TRANS_INTRA, 
COALESCE(MONTO_TRANS_INTRA,0) AS MONTO_TRANS_INTRA,
COALESCE(NUM_CASH_OUT,0) AS NUM_CASH_OUT,
COALESCE(MONTO_CASH_OUT,0) AS MONTO_CASH_OUT, 
COALESCE(NUM_RECARGA,0) AS NUM_RECARGA,
COALESCE(MONTO_RECARGA,0) AS MONTO_RECARGA,
COALESCE(NUM_PAGO_COMERCIO,0) AS NUM_PAGO_COMERCIO,
COALESCE(MONTO_PAGO_COMERCIO,0) AS MONTO_PAGO_COMERCIO,
COALESCE(NUM_PAGO_PROV,0) AS NUM_PAGO_PROV, 
COALESCE(MONTO_PAGO_PROV,0) AS MONTO_PAGO_PROV,
COALESCE(NUM_PAGO_SERVICIO,0) AS NUM_PAGO_SERVICIO,
COALESCE(MONTO_PAGO_SERVICIO,0) AS MONTO_PAGO_SERVICIO, 
COALESCE(NUM_OTRO,0) AS NUM_OTRO,
COALESCE(MONTO_OTRO,0) AS MONTO_OTRO, 
COALESCE(MONEDA,'PEN') AS MONEDA
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID 
LEFT JOIN DATA_FINAL DF ON ID.ISSUER_CODE = DF.EMISOR
UNION ALL
SELECT 
'TOTAL DIARIO' AS EMISOR,
SUM(NUM_CASH_IN) AS NUM_CASH_IN,
SUM(MONTO_CASH_IN) AS MONTO_CASH_IN,
SUM(NUM_TRANS_REC) AS NUM_TRANS_REC,
SUM(MONTO_TRANS_REC) AS MONTO_TRANS_REC,
SUM(NUM_TRANS_ENV) AS NUM_TRANS_ENV,
SUM(MONTO_TRANS_ENV) AS MONTO_TRANS_ENV, 
SUM(NUM_TRANS_INTRA) AS NUM_TRANS_INTRA, 
SUM(MONTO_TRANS_INTRA) AS MONTO_TRANS_INTRA,
SUM(NUM_CASH_OUT) AS NUM_CASH_OUT,
SUM(MONTO_CASH_OUT) AS MONTO_CASH_OUT, 
SUM(NUM_RECARGA) AS NUM_RECARGA,
SUM(MONTO_RECARGA) AS MONTO_RECARGA,
SUM(NUM_PAGO_COMERCIO) AS NUM_PAGO_COMERCIO,
SUM(MONTO_PAGO_COMERCIO) AS MONTO_PAGO_COMERCIO,
SUM(NUM_PAGO_PROV) AS NUM_PAGO_PROV, 
SUM(MONTO_PAGO_PROV) AS MONTO_PAGO_PROV,
SUM(NUM_PAGO_SERVICIO) AS NUM_PAGO_SERVICIO,
SUM(MONTO_PAGO_SERVICIO) AS MONTO_PAGO_SERVICIO,
SUM(NUM_OTRO) AS NUM_OTRO,
SUM(MONTO_OTRO) AS MONTO_OTRO,
'PEN' AS MONEDA
FROM DATA_FINAL

[<src.core.models.report_row.ReportRow object at 0x7f77aece03a0>, <src.core.models.report_row.ReportRow object at 0x7f77aece0280>, <src.core.models.report_row.ReportRow object at 0x7f77aece04c0>, <src.core.models.report_row.ReportRow object at 0x7f77aece0520>, <src.core.models.report_row.ReportRow object at 0x7f77aece0580>, <src.core.models.report_row.ReportRow object at 0x7f77aece05e0>, <src.core.models.report_row.ReportRow object at 0x7f77aece0640>]
Archivo guardado correctamente en /home/<USER>/output/csv/BCRP-OPERACIONES_POR_EMISOR-**************.csv
