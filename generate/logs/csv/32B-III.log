WITH
USER_DATA_INIT AS (
SELECT
	UP.USER_ID,
	MW.WALLET_NUMBER,
	UP.GENDER,
	KC.ID_TYPE,
	MC.CATEGORY_NAME,
	CASE 
		WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN 'MERCHANT GENERAL ACCOUNT PROFILE'
		ELSE CG.GRADE_NAME 
	END AS PERFIL,
	ID.ISSUER_CODE,
	UP.CREATED_ON,
	UP.MODIFIED_ON,
	SSI.STATUS_CODE,
	SPM.SUBTYPE_NAME,
	SSI_ACC.STATUS_CODE AS STATUS_ACC,
	MW.CREATED_ON AS CREATED_ON_ACC,
	MW.MODIFIED_ON AS MODIFIED_ON_ACC
FROM PDP_PROD10_MAINDB.USER_PROFILE UP
INNER JOIN PDP_PROD10_MAINDB.MTX_CATEGORIES MC ON UP.CATEGORY_ID = MC.CATEGORY_CODE
LEFT JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON UP.USER_ID = MW.USER_ID
INNER JOIN PDP_PROD10_MAINDB.SYS_PAYMENT_METHOD_SUBTYPES SPM ON MW.PAYMENT_TYPE_ID = SPM.PAYMENT_TYPE_ID
LEFT JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
LEFT JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON MW.ISSUER_ID = ID.ISSUER_ID
INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KC ON UP.KYC_ID = KC.KYC_ID
INNER JOIN PDP_PROD10_MAINDB.SYS_STATUS_ITEM SSI ON UP.STATUS = SSI.STATUS_ID 
INNER JOIN PDP_PROD10_MAINDB.SYS_STATUS_ITEM SSI_ACC ON MW.STATUS = SSI_ACC.STATUS_ID
WHERE TRUNC(UP.CREATED_ON) <= (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES)
AND PROVIDER_ID = 101 --SOLES
),
USER_DATA_END AS (
SELECT
	UP.USER_ID,
	MW.WALLET_NUMBER,
	UP.GENDER,
	KC.ID_TYPE,
	MC.CATEGORY_NAME,
	CASE 
		WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN 'MERCHANT GENERAL ACCOUNT PROFILE'
		ELSE CG.GRADE_NAME 
	END AS PERFIL,
	ID.ISSUER_CODE,
	UP.CREATED_ON,
	UP.MODIFIED_ON,
	SSI.STATUS_CODE,
	SPM.SUBTYPE_NAME,
	SSI_ACC.STATUS_CODE AS STATUS_ACC,
	MW.CREATED_ON AS CREATED_ON_ACC,
	MW.MODIFIED_ON AS MODIFIED_ON_ACC
FROM PDP_PROD10_MAINDB.USER_PROFILE UP
INNER JOIN PDP_PROD10_MAINDB.MTX_CATEGORIES MC ON UP.CATEGORY_ID = MC.CATEGORY_CODE
LEFT JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON UP.USER_ID = MW.USER_ID
INNER JOIN PDP_PROD10_MAINDB.SYS_PAYMENT_METHOD_SUBTYPES SPM ON MW.PAYMENT_TYPE_ID = SPM.PAYMENT_TYPE_ID
LEFT JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
LEFT JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON MW.ISSUER_ID = ID.ISSUER_ID
INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KC ON UP.KYC_ID = KC.KYC_ID
INNER JOIN PDP_PROD10_MAINDB.SYS_STATUS_ITEM SSI ON UP.STATUS = SSI.STATUS_ID 
INNER JOIN PDP_PROD10_MAINDB.SYS_STATUS_ITEM SSI_ACC ON MW.STATUS = SSI_ACC.STATUS_ID 
WHERE TRUNC(UP.CREATED_ON) <= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) --CAMBIAR A LA FECHA CIERRE DE DIA 
AND PROVIDER_ID = 101 --SOLES
),
DATA_INICIO_FIN AS (
SELECT 
	I.*,
	'INIT' AS ORIGEN,
	CASE 
		WHEN I.GENDER = 'GEN_FEM' THEN 'FEMENINO'
		WHEN I.GENDER = 'GEN_MAL' THEN 'MASCULINO'
		ELSE 'OTROS'
	END AS GENERO,
	CASE 
		WHEN UPPER(I.PERFIL) IN ('MERCHANT ACCOUNT PROFILE','NORMAL ACCOUNT PROFILE') AND I.ID_TYPE IN ('DNI','CE') THEN 'SIMPLIFICADA'
		ELSE 'GENERAL'
	END AS TIPO_CUENTA
FROM USER_DATA_INIT I
UNION ALL
SELECT 
	E.*,
	'END' AS ORIGEN,
	CASE 
		WHEN E.GENDER = 'GEN_FEM' THEN 'FEMENINO'
		WHEN E.GENDER = 'GEN_MAL' THEN 'MASCULINO'
		ELSE 'OTROS'
	END AS GENERO,
	CASE 
		WHEN UPPER(E.PERFIL) IN ('MERCHANT ACCOUNT PROFILE','NORMAL ACCOUNT PROFILE') AND E.ID_TYPE IN ('DNI','CE')  THEN 'SIMPLIFICADA'
		ELSE 'GENERAL'
	END AS TIPO_CUENTA
FROM USER_DATA_END E
),
BLOQUEADOS_MES AS (
SELECT WALLET_NUMBER, CREATED_ON AS LOCK_DATE
FROM PDP_PROD10_MAINDB.MTX_PARTY_BLACK_LIST UMH
WHERE 1=1
AND TRUNC(UMH.CREATED_ON) >= (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES)
AND TRUNC(UMH.CREATED_ON) <= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) --CAMBIAR A FECHA DE CIERRE DE CORTE
),
DATA_GENERAL AS (
SELECT 
	DG.*,
	CASE 
		WHEN DG.TIPO_CUENTA IN 'GENERAL' AND DG.ID_TYPE IN ('DNI','CE','OTHER') AND GENERO <> 'OTROS' THEN 'NATURAL'
		WHEN DG.TIPO_CUENTA IN 'GENERAL' AND (DG.ID_TYPE = 'RUC' OR DG.ID_TYPE IS NULL) THEN 'JURIDICA'
		WHEN DG.TIPO_CUENTA IN 'SIMPLIFICADA'  AND GENERO <> 'OTROS' THEN 'NATURAL'
	END AS TIPO_PERSONA,
	BM.LOCK_DATE,
	CASE WHEN BM.WALLET_NUMBER IS NOT NULL THEN 1 ELSE 0 END AS CUENTA_BLOQUEADA
FROM DATA_INICIO_FIN DG
LEFT JOIN BLOQUEADOS_MES BM ON DG.WALLET_NUMBER = BM.WALLET_NUMBER 
),
DATA_CONTEO AS (
SELECT 
	ORIGEN,
	ISSUER_CODE,
	TIPO_PERSONA,
	GENERO,
	CASE WHEN CUENTA_BLOQUEADA = 1 AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(LOCK_DATE) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_BLQOUEADA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN CUENTA_BLOQUEADA = 1 AND TIPO_CUENTA = 'GENERAL' AND TRUNC(LOCK_DATE) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_BLQOUEADA_GEN, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	--
	CASE WHEN CUENTA_BLOQUEADA = 1 AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(LOCK_DATE) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_BLQOUEADA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN CUENTA_BLOQUEADA = 1 AND TIPO_CUENTA = 'GENERAL' AND TRUNC(LOCK_DATE) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_BLQOUEADA_GEN,--COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	-------------------------
	CASE WHEN STATUS_ACC = 'ACTIVE' AND TIPO_CUENTA = 'SIMPLIFICADA' THEN 1 ELSE 0 END AS CUENTA_VIGENTE_SIMP,
	CASE WHEN STATUS_CODE = 'ACTIVE' AND TIPO_CUENTA = 'SIMPLIFICADA' THEN 1 ELSE 0 END AS TITULAR_VIGENTE_SIMP,
	--
	CASE WHEN STATUS_ACC = 'ACTIVE' AND TIPO_CUENTA = 'GENERAL' THEN 1 ELSE 0 END AS CUENTA_VIGENTE_GEN,
	CASE WHEN STATUS_CODE = 'ACTIVE' AND TIPO_CUENTA = 'GENERAL' THEN 1 ELSE 0 END AS TITULAR_VIGENTE_GEN,
	------------------------------------------
	CASE WHEN STATUS_ACC = 'ACTIVE' AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(CREATED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_ABIERTA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN STATUS_ACC = 'ACTIVE' AND TIPO_CUENTA = 'GENERAL' AND TRUNC(CREATED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_ABIERTA_GEN, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	--
	CASE WHEN STATUS_CODE = 'ACTIVE' AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(CREATED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_ABIERTA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN STATUS_CODE = 'ACTIVE' AND TIPO_CUENTA = 'GENERAL' AND TRUNC(CREATED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_ABIERTA_GEN, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	--
	CASE WHEN STATUS_ACC = 'DELETE' AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(MODIFIED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_CERRADA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN STATUS_ACC = 'DELETE' AND TIPO_CUENTA = 'GENERAL' AND TRUNC(MODIFIED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_CERRADA_GEN, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	--
	CASE WHEN STATUS_CODE = 'DELETE' AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(MODIFIED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_CERRADA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN STATUS_CODE = 'DELETE' AND TIPO_CUENTA = 'GENERAL' AND TRUNC(MODIFIED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_CERRADA_GEN, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	--
	CASE WHEN GENERO = 'FEMENINO' THEN 1 ELSE 0 END AS FEMENINO,
	CASE WHEN GENERO = 'MASCULINO' THEN 1 ELSE 0 END AS MASCULINO,
	CASE WHEN TIPO_CUENTA = 'GENERAL' THEN 1 ELSE 0 END AS GENERALES,
	CASE WHEN TIPO_CUENTA = 'SIMPLIFICADA' THEN 1 ELSE 0 END AS SIMPLIFICADA
FROM DATA_GENERAL 
WHERE SUBTYPE_NAME = 'Mobile Money'
AND TIPO_PERSONA IN ('NATURAL','JURIDICA')
),
DATA_AGRUPADA AS (
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'100' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE ORIGEN='INIT'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'110' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND ORIGEN='INIT' AND GENERO <> 'OTROS'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'115' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND FEMENINO = 1 AND ORIGEN='INIT'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'118' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND MASCULINO = 1 AND ORIGEN='INIT'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'120' FILA, 
	0 NROCUENTAS_SIMP, 
	0 NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'JURIDICA' AND ORIGEN='INIT'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
------------------------------------------------------------
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'200' FILA, 
	COALESCE(SUM(CUENTA_ABIERTA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_ABIERTA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_ABIERTA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_ABIERTA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'210' FILA, 
	COALESCE(SUM(CUENTA_ABIERTA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_ABIERTA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_ABIERTA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_ABIERTA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'220' FILA, 
	COALESCE(SUM(CUENTA_ABIERTA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_ABIERTA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_ABIERTA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_ABIERTA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'JURIDICA'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
------------------------------------------------------------
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'300' FILA, 
	COALESCE(SUM(CUENTA_CERRADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_CERRADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_CERRADA_GEN),0) NROCUENTAS_GEN,
	COALESCE(SUM(TITULAR_CERRADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'310' FILA, 
	COALESCE(SUM(CUENTA_CERRADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_CERRADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_CERRADA_GEN),0) NROCUENTAS_GEN,
	COALESCE(SUM(TITULAR_CERRADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'320' FILA, 
	COALESCE(SUM(CUENTA_CERRADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_CERRADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_CERRADA_GEN),0) NROCUENTAS_GEN,
	COALESCE(SUM(TITULAR_CERRADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'JURIDICA'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
------------------------------------------------------------
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'400' FILA, 
	COALESCE(SUM(CUENTA_BLQOUEADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_BLQOUEADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_BLQOUEADA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_BLQOUEADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'410' FILA, 
	COALESCE(SUM(CUENTA_BLQOUEADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_BLQOUEADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_BLQOUEADA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_BLQOUEADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'420' FILA, 
	COALESCE(SUM(CUENTA_BLQOUEADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_BLQOUEADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_BLQOUEADA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_BLQOUEADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'JURIDICA'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
---------------------------------------------------------------
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'500' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE ORIGEN='END'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'510' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND ORIGEN='END'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'515' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND FEMENINO = 1 AND ORIGEN='END' 
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'518' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND MASCULINO = 1 AND ORIGEN='END'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'520' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'JURIDICA' AND ORIGEN='END'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
)
--select * from DATA_GENERAL
SELECT 
	REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'','') AS EMISOR,
	LPAD(FILA,6,'0'),
	LPAD(NROCUENTAS_SIMP,18,'0'),
	LPAD(NROTITULARES_SIMP,18,'0'),
	LPAD(NROCUENTAS_GEN,18,'0'),
	LPAD(NROTITULARES_GEN,18,'0')
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID
LEFT JOIN DATA_AGRUPADA DA ON ID.ISSUER_CODE = DA.EMISOR


--EJECUTAR Y GUARDAR CON EL NOMBRE 32B-III-YYYYMMDD.csv (********)

Ejecutando query: WITH
USER_DATA_INIT AS (
SELECT
	UP.USER_ID,
	MW.WALLET_NUMBER,
	UP.GENDER,
	KC.ID_TYPE,
	MC.CATEGORY_NAME,
	CASE 
		WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN 'MERCHANT GENERAL ACCOUNT PROFILE'
		ELSE CG.GRADE_NAME 
	END AS PERFIL,
	ID.ISSUER_CODE,
	UP.CREATED_ON,
	UP.MODIFIED_ON,
	SSI.STATUS_CODE,
	SPM.SUBTYPE_NAME,
	SSI_ACC.STATUS_CODE AS STATUS_ACC,
	MW.CREATED_ON AS CREATED_ON_ACC,
	MW.MODIFIED_ON AS MODIFIED_ON_ACC
FROM PDP_PROD10_MAINDB.USER_PROFILE UP
INNER JOIN PDP_PROD10_MAINDB.MTX_CATEGORIES MC ON UP.CATEGORY_ID = MC.CATEGORY_CODE
LEFT JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON UP.USER_ID = MW.USER_ID
INNER JOIN PDP_PROD10_MAINDB.SYS_PAYMENT_METHOD_SUBTYPES SPM ON MW.PAYMENT_TYPE_ID = SPM.PAYMENT_TYPE_ID
LEFT JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
LEFT JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON MW.ISSUER_ID = ID.ISSUER_ID
INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KC ON UP.KYC_ID = KC.KYC_ID
INNER JOIN PDP_PROD10_MAINDB.SYS_STATUS_ITEM SSI ON UP.STATUS = SSI.STATUS_ID 
INNER JOIN PDP_PROD10_MAINDB.SYS_STATUS_ITEM SSI_ACC ON MW.STATUS = SSI_ACC.STATUS_ID
WHERE TRUNC(UP.CREATED_ON) <= (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES)
AND PROVIDER_ID = 101 --SOLES
),
USER_DATA_END AS (
SELECT
	UP.USER_ID,
	MW.WALLET_NUMBER,
	UP.GENDER,
	KC.ID_TYPE,
	MC.CATEGORY_NAME,
	CASE 
		WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN 'MERCHANT GENERAL ACCOUNT PROFILE'
		ELSE CG.GRADE_NAME 
	END AS PERFIL,
	ID.ISSUER_CODE,
	UP.CREATED_ON,
	UP.MODIFIED_ON,
	SSI.STATUS_CODE,
	SPM.SUBTYPE_NAME,
	SSI_ACC.STATUS_CODE AS STATUS_ACC,
	MW.CREATED_ON AS CREATED_ON_ACC,
	MW.MODIFIED_ON AS MODIFIED_ON_ACC
FROM PDP_PROD10_MAINDB.USER_PROFILE UP
INNER JOIN PDP_PROD10_MAINDB.MTX_CATEGORIES MC ON UP.CATEGORY_ID = MC.CATEGORY_CODE
LEFT JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET MW ON UP.USER_ID = MW.USER_ID
INNER JOIN PDP_PROD10_MAINDB.SYS_PAYMENT_METHOD_SUBTYPES SPM ON MW.PAYMENT_TYPE_ID = SPM.PAYMENT_TYPE_ID
LEFT JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
LEFT JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON MW.ISSUER_ID = ID.ISSUER_ID
INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KC ON UP.KYC_ID = KC.KYC_ID
INNER JOIN PDP_PROD10_MAINDB.SYS_STATUS_ITEM SSI ON UP.STATUS = SSI.STATUS_ID 
INNER JOIN PDP_PROD10_MAINDB.SYS_STATUS_ITEM SSI_ACC ON MW.STATUS = SSI_ACC.STATUS_ID 
WHERE TRUNC(UP.CREATED_ON) <= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) --CAMBIAR A LA FECHA CIERRE DE DIA 
AND PROVIDER_ID = 101 --SOLES
),
DATA_INICIO_FIN AS (
SELECT 
	I.*,
	'INIT' AS ORIGEN,
	CASE 
		WHEN I.GENDER = 'GEN_FEM' THEN 'FEMENINO'
		WHEN I.GENDER = 'GEN_MAL' THEN 'MASCULINO'
		ELSE 'OTROS'
	END AS GENERO,
	CASE 
		WHEN UPPER(I.PERFIL) IN ('MERCHANT ACCOUNT PROFILE','NORMAL ACCOUNT PROFILE') AND I.ID_TYPE IN ('DNI','CE') THEN 'SIMPLIFICADA'
		ELSE 'GENERAL'
	END AS TIPO_CUENTA
FROM USER_DATA_INIT I
UNION ALL
SELECT 
	E.*,
	'END' AS ORIGEN,
	CASE 
		WHEN E.GENDER = 'GEN_FEM' THEN 'FEMENINO'
		WHEN E.GENDER = 'GEN_MAL' THEN 'MASCULINO'
		ELSE 'OTROS'
	END AS GENERO,
	CASE 
		WHEN UPPER(E.PERFIL) IN ('MERCHANT ACCOUNT PROFILE','NORMAL ACCOUNT PROFILE') AND E.ID_TYPE IN ('DNI','CE')  THEN 'SIMPLIFICADA'
		ELSE 'GENERAL'
	END AS TIPO_CUENTA
FROM USER_DATA_END E
),
BLOQUEADOS_MES AS (
SELECT WALLET_NUMBER, CREATED_ON AS LOCK_DATE
FROM PDP_PROD10_MAINDB.MTX_PARTY_BLACK_LIST UMH
WHERE 1=1
AND TRUNC(UMH.CREATED_ON) >= (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES)
AND TRUNC(UMH.CREATED_ON) <= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) --CAMBIAR A FECHA DE CIERRE DE CORTE
),
DATA_GENERAL AS (
SELECT 
	DG.*,
	CASE 
		WHEN DG.TIPO_CUENTA IN 'GENERAL' AND DG.ID_TYPE IN ('DNI','CE','OTHER') AND GENERO <> 'OTROS' THEN 'NATURAL'
		WHEN DG.TIPO_CUENTA IN 'GENERAL' AND (DG.ID_TYPE = 'RUC' OR DG.ID_TYPE IS NULL) THEN 'JURIDICA'
		WHEN DG.TIPO_CUENTA IN 'SIMPLIFICADA'  AND GENERO <> 'OTROS' THEN 'NATURAL'
	END AS TIPO_PERSONA,
	BM.LOCK_DATE,
	CASE WHEN BM.WALLET_NUMBER IS NOT NULL THEN 1 ELSE 0 END AS CUENTA_BLOQUEADA
FROM DATA_INICIO_FIN DG
LEFT JOIN BLOQUEADOS_MES BM ON DG.WALLET_NUMBER = BM.WALLET_NUMBER 
),
DATA_CONTEO AS (
SELECT 
	ORIGEN,
	ISSUER_CODE,
	TIPO_PERSONA,
	GENERO,
	CASE WHEN CUENTA_BLOQUEADA = 1 AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(LOCK_DATE) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_BLQOUEADA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN CUENTA_BLOQUEADA = 1 AND TIPO_CUENTA = 'GENERAL' AND TRUNC(LOCK_DATE) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_BLQOUEADA_GEN, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	--
	CASE WHEN CUENTA_BLOQUEADA = 1 AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(LOCK_DATE) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_BLQOUEADA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN CUENTA_BLOQUEADA = 1 AND TIPO_CUENTA = 'GENERAL' AND TRUNC(LOCK_DATE) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_BLQOUEADA_GEN,--COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	-------------------------
	CASE WHEN STATUS_ACC = 'ACTIVE' AND TIPO_CUENTA = 'SIMPLIFICADA' THEN 1 ELSE 0 END AS CUENTA_VIGENTE_SIMP,
	CASE WHEN STATUS_CODE = 'ACTIVE' AND TIPO_CUENTA = 'SIMPLIFICADA' THEN 1 ELSE 0 END AS TITULAR_VIGENTE_SIMP,
	--
	CASE WHEN STATUS_ACC = 'ACTIVE' AND TIPO_CUENTA = 'GENERAL' THEN 1 ELSE 0 END AS CUENTA_VIGENTE_GEN,
	CASE WHEN STATUS_CODE = 'ACTIVE' AND TIPO_CUENTA = 'GENERAL' THEN 1 ELSE 0 END AS TITULAR_VIGENTE_GEN,
	------------------------------------------
	CASE WHEN STATUS_ACC = 'ACTIVE' AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(CREATED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_ABIERTA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN STATUS_ACC = 'ACTIVE' AND TIPO_CUENTA = 'GENERAL' AND TRUNC(CREATED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_ABIERTA_GEN, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	--
	CASE WHEN STATUS_CODE = 'ACTIVE' AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(CREATED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_ABIERTA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN STATUS_CODE = 'ACTIVE' AND TIPO_CUENTA = 'GENERAL' AND TRUNC(CREATED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_ABIERTA_GEN, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	--
	CASE WHEN STATUS_ACC = 'DELETE' AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(MODIFIED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_CERRADA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN STATUS_ACC = 'DELETE' AND TIPO_CUENTA = 'GENERAL' AND TRUNC(MODIFIED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS CUENTA_CERRADA_GEN, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	--
	CASE WHEN STATUS_CODE = 'DELETE' AND TIPO_CUENTA = 'SIMPLIFICADA' AND TRUNC(MODIFIED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_CERRADA_SIMP, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	CASE WHEN STATUS_CODE = 'DELETE' AND TIPO_CUENTA = 'GENERAL' AND TRUNC(MODIFIED_ON_ACC) BETWEEN (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) AND TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')) THEN 1 ELSE 0 END AS TITULAR_CERRADA_GEN, --COLOCAR EL PRIMER SYSDATE PARA QUE DE LA FECHA 11/03 HASTA EL D-1
	--
	CASE WHEN GENERO = 'FEMENINO' THEN 1 ELSE 0 END AS FEMENINO,
	CASE WHEN GENERO = 'MASCULINO' THEN 1 ELSE 0 END AS MASCULINO,
	CASE WHEN TIPO_CUENTA = 'GENERAL' THEN 1 ELSE 0 END AS GENERALES,
	CASE WHEN TIPO_CUENTA = 'SIMPLIFICADA' THEN 1 ELSE 0 END AS SIMPLIFICADA
FROM DATA_GENERAL 
WHERE SUBTYPE_NAME = 'Mobile Money'
AND TIPO_PERSONA IN ('NATURAL','JURIDICA')
),
DATA_AGRUPADA AS (
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'100' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE ORIGEN='INIT'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'110' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND ORIGEN='INIT' AND GENERO <> 'OTROS'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'115' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND FEMENINO = 1 AND ORIGEN='INIT'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'118' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND MASCULINO = 1 AND ORIGEN='INIT'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'120' FILA, 
	0 NROCUENTAS_SIMP, 
	0 NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'JURIDICA' AND ORIGEN='INIT'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
------------------------------------------------------------
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'200' FILA, 
	COALESCE(SUM(CUENTA_ABIERTA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_ABIERTA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_ABIERTA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_ABIERTA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'210' FILA, 
	COALESCE(SUM(CUENTA_ABIERTA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_ABIERTA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_ABIERTA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_ABIERTA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'220' FILA, 
	COALESCE(SUM(CUENTA_ABIERTA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_ABIERTA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_ABIERTA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_ABIERTA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'JURIDICA'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
------------------------------------------------------------
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'300' FILA, 
	COALESCE(SUM(CUENTA_CERRADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_CERRADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_CERRADA_GEN),0) NROCUENTAS_GEN,
	COALESCE(SUM(TITULAR_CERRADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'310' FILA, 
	COALESCE(SUM(CUENTA_CERRADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_CERRADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_CERRADA_GEN),0) NROCUENTAS_GEN,
	COALESCE(SUM(TITULAR_CERRADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'320' FILA, 
	COALESCE(SUM(CUENTA_CERRADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_CERRADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_CERRADA_GEN),0) NROCUENTAS_GEN,
	COALESCE(SUM(TITULAR_CERRADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'JURIDICA'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
------------------------------------------------------------
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'400' FILA, 
	COALESCE(SUM(CUENTA_BLQOUEADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_BLQOUEADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_BLQOUEADA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_BLQOUEADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'410' FILA, 
	COALESCE(SUM(CUENTA_BLQOUEADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_BLQOUEADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_BLQOUEADA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_BLQOUEADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'420' FILA, 
	COALESCE(SUM(CUENTA_BLQOUEADA_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_BLQOUEADA_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_BLQOUEADA_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_BLQOUEADA_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'JURIDICA'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
---------------------------------------------------------------
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'500' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE ORIGEN='END'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'510' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND ORIGEN='END'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'515' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND FEMENINO = 1 AND ORIGEN='END' 
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'518' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'NATURAL' AND MASCULINO = 1 AND ORIGEN='END'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
UNION
SELECT 
	ID.ISSUER_CODE AS EMISOR, 
	'520' FILA, 
	COALESCE(SUM(CUENTA_VIGENTE_SIMP),0) NROCUENTAS_SIMP, 
	COALESCE(SUM(TITULAR_VIGENTE_SIMP),0) NROTITULARES_SIMP,
	COALESCE(SUM(CUENTA_VIGENTE_GEN),0) NROCUENTAS_GEN, 
	COALESCE(SUM(TITULAR_VIGENTE_GEN),0) NROTITULARES_GEN
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_CONTEO 
WHERE TIPO_PERSONA = 'JURIDICA' AND ORIGEN='END'
) DC ON ID.ISSUER_CODE = DC.ISSUER_CODE GROUP BY ID.ISSUER_CODE
)
--select * from DATA_GENERAL
SELECT 
	REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'','') AS EMISOR,
	LPAD(FILA,6,'0'),
	LPAD(NROCUENTAS_SIMP,18,'0'),
	LPAD(NROTITULARES_SIMP,18,'0'),
	LPAD(NROCUENTAS_GEN,18,'0'),
	LPAD(NROTITULARES_GEN,18,'0')
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID
LEFT JOIN DATA_AGRUPADA DA ON ID.ISSUER_CODE = DA.EMISOR


--EJECUTAR Y GUARDAR CON EL NOMBRE 32B-III-YYYYMMDD.csv (********)
2025-06-12 14:57:23,816 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-06-12 14:57:23,818 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/32B-III-20250613.csv

[<src.core.models.report_row.ReportRow object at 0x7f70b86d7790>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7670>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7820>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7610>, <src.core.models.report_row.ReportRow object at 0x7f70b86d75e0>, <src.core.models.report_row.ReportRow object at 0x7f70b86d77f0>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7640>, <src.core.models.report_row.ReportRow object at 0x7f70b86d76d0>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7700>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7880>, <src.core.models.report_row.ReportRow object at 0x7f70b86d78e0>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7940>, <src.core.models.report_row.ReportRow object at 0x7f70b86d79a0>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7a00>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7a60>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7ac0>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7b20>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7b80>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7be0>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7c40>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7ca0>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7d00>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7f40>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7fa0>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7e80>, <src.core.models.report_row.ReportRow object at 0x7f70b86d77c0>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7e20>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2250>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c22b0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2310>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2370>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c23d0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2430>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2490>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c24f0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2550>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c25b0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2610>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2670>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c26d0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2730>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2790>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c27f0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2850>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c28b0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2910>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2970>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c29d0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2a30>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2a90>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2af0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2b50>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2bb0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2c10>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2c70>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2cd0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2d30>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2d90>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2df0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2e50>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2eb0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2f10>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2f70>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2fd0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c20a0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2070>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2160>, <src.core.models.report_row.ReportRow object at 0x7f70b86d7d60>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c2040>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7250>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c72b0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7310>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7370>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c73d0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7430>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7490>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c74f0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7550>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c75b0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7610>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7670>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c76d0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7730>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7790>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c77f0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7850>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c78b0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7910>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7970>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c79d0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7a30>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7a90>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7af0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7b50>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7bb0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7c10>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7c70>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7cd0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7d30>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7d90>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7df0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7e50>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7eb0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7f10>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7f70>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7fd0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c70a0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7070>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7160>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c20d0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9c7040>, <src.core.models.report_row.ReportRow object at 0x7f70bc9ce250>, <src.core.models.report_row.ReportRow object at 0x7f70bc9ce2b0>, <src.core.models.report_row.ReportRow object at 0x7f70bc9ce310>]
