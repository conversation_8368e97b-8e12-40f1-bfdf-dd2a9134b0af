2025-06-12 14:55:47,253 - root - INFO - <PERSON>tos encontrados. Procediendo a exportar el archivo CSV.
2025-06-12 14:55:47,255 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/32B-IV-20250613.csv
WITH
INIT_MES AS (
SELECT
	REPLACE(ID.ISSUER_CODE,'0144','')  AS EMISOR,
	SUM(MONTO) AS TOTAL
FROM USR_DATALAKE.USER_BALANCES BI
INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON BI.ISSUER_ID = ID.ISSUER_ID AND BI.FECHA_ORIGEN = (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES)
GROUP BY ID.ISSUER_CODE
),
END_MES AS (
SELECT
	REPLACE(ID.ISSUER_CODE,'0144','')  AS EMISOR,
	SUM(MONTO) AS TOTAL
FROM USR_DATALAKE.USER_BALANCES BI
INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON BI.ISSUER_ID = ID.ISSUER_ID AND BI.FECHA_ORIGEN = TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')
GROUP BY ID.ISSUER_CODE
)
SELECT 
	EMISOR,
	LPAD('100',6,'0') || LPAD(CAST(TOTAL AS VARCHAR(80)),18,'0') || LPAD('0',54,'0') AS TRAMA
FROM INIT_MES
UNION 
SELECT 
	EMISOR,
	LPAD('200',6,'0') || LPAD(CAST(TOTAL AS VARCHAR(80)),18,'0') || LPAD('0',54,'0') AS TRAMA
FROM END_MES


--EJECUTAR Y GUARDAR CON EL NOMBRE 32B-IV-YYYYMMDD.csv (20250314)

Ejecutando query: WITH
INIT_MES AS (
SELECT
	REPLACE(ID.ISSUER_CODE,'0144','')  AS EMISOR,
	SUM(MONTO) AS TOTAL
FROM USR_DATALAKE.USER_BALANCES BI
INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON BI.ISSUER_ID = ID.ISSUER_ID AND BI.FECHA_ORIGEN = (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES)
GROUP BY ID.ISSUER_CODE
),
END_MES AS (
SELECT
	REPLACE(ID.ISSUER_CODE,'0144','')  AS EMISOR,
	SUM(MONTO) AS TOTAL
FROM USR_DATALAKE.USER_BALANCES BI
INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON BI.ISSUER_ID = ID.ISSUER_ID AND BI.FECHA_ORIGEN = TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')
GROUP BY ID.ISSUER_CODE
)
SELECT 
	EMISOR,
	LPAD('100',6,'0') || LPAD(CAST(TOTAL AS VARCHAR(80)),18,'0') || LPAD('0',54,'0') AS TRAMA
FROM INIT_MES
UNION 
SELECT 
	EMISOR,
	LPAD('200',6,'0') || LPAD(CAST(TOTAL AS VARCHAR(80)),18,'0') || LPAD('0',54,'0') AS TRAMA
FROM END_MES


--EJECUTAR Y GUARDAR CON EL NOMBRE 32B-IV-YYYYMMDD.csv (20250314)

[<src.core.models.report_row.ReportRow object at 0x7fee593186d0>, <src.core.models.report_row.ReportRow object at 0x7fee593185b0>, <src.core.models.report_row.ReportRow object at 0x7fee593187f0>, <src.core.models.report_row.ReportRow object at 0x7fee59318850>, <src.core.models.report_row.ReportRow object at 0x7fee593188b0>]
