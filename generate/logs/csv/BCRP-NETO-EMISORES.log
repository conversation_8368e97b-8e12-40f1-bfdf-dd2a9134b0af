2025-06-13 06:37:48,104 - root - INFO - <PERSON><PERSON> encontrados. Procediendo a exportar el archivo CSV.
2025-06-13 06:37:48,106 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/BCRP-NETO_PAGAR_ENTRE_EMISORES-**************.csv
2025-06-13 06:37:48,118 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-13 06:37:48,276 - root - INFO - Archivo: BCRP-NETO_PAGAR_ENTRE_EMISORES-**************.csv subido a: s3://prd-datalake-reports-************/BCRP/2025-06-13/BCRP-NETO-EMISORES/BCRP-NETO_PAGAR_ENTRE_EMISORES-**************.csv
WITH
DATA_TRX AS (
	SELECT
	"TransactionID" as TRX_ID,
	"Amount" as <PERSON><PERSON><PERSON>,
	"From_BankDomain" as <PERSON><PERSON><PERSON>,
	"To_BankDomain" as <PERSON><PERSON><PERSON>,
	"<PERSON>urrency" as <PERSON><PERSON><PERSON><PERSON>,
	"DateTime" AS FECHA
	FROM USR_DATALAKE.LOG_TRX_FINAL
	WHERE "From_BankDomain" <> "To_BankDomain"
	AND TRUNC(TO_DATE("DateTime",'YYYY/MM/DD HH24:MI:SS')) >= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
),
PRE_ENVIADO AS (
	SELECT 
	PAYER.PAYER,
	PAYER.PAYEE,
	PAYER.MONEDA,
	SUM(PAYER.MONTO) AS MONTO_ENVIADO
	FROM DATA_TRX PAYER
	GROUP BY 
	PAYER.PAYER,
	PAYER.PAYEE,
	PAYER.MONEDA
),
PRE_FINAL AS (
	SELECT 
	P1.PAYER,
	P1.PAYEE,
	P1.MONEDA,
	COALESCE(SUM(P1.MONTO_ENVIADO),0) AS MONTO_ENVIADO,
	COALESCE(SUM(P2.MONTO_ENVIADO),0) AS MONTO_RECIBIDO
	FROM PRE_ENVIADO P1
	LEFT JOIN PRE_ENVIADO P2
	ON P1.PAYEE = P2.PAYER AND P1.PAYER = P2.PAYEE
	GROUP BY 
	P1.PAYER,
	P1.PAYEE,
	P1.MONEDA
)
SELECT 
REPLACE(REPLACE(ID1.ISSUER_CODE,'0144',''),'0231','') ORIGEN,
REPLACE(REPLACE(ID2.ISSUER_CODE,'0144',''),'0231','') DESTINO,
COALESCE(PF.MONTO_ENVIADO,0) AS MONTO_ENVIADO,
COALESCE(PF.MONTO_RECIBIDO,0) AS MONTO_RECIBIDO,
COALESCE((PF.MONTO_ENVIADO-PF.MONTO_RECIBIDO),0) AS MONTO_NETO,
COALESCE(PF.MONEDA,'PEN') AS MONEDA
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID1
INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID2 ON ID1.ISSUER_CODE <> ID2.ISSUER_CODE
LEFT JOIN PRE_FINAL PF ON REPLACE(REPLACE(ID1.ISSUER_CODE,'0144',''),'','') = PF.PAYER AND REPLACE(REPLACE(ID2.ISSUER_CODE,'0144',''),'','') = PF.PAYEE

Ejecutando query: WITH
DATA_TRX AS (
	SELECT
	"TransactionID" as TRX_ID,
	"Amount" as MONTO,
	"From_BankDomain" as PAYER,
	"To_BankDomain" as PAYEE,
	"Currency" as MONEDA,
	"DateTime" AS FECHA
	FROM USR_DATALAKE.LOG_TRX_FINAL
	WHERE "From_BankDomain" <> "To_BankDomain"
	AND TRUNC(TO_DATE("DateTime",'YYYY/MM/DD HH24:MI:SS')) >= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
),
PRE_ENVIADO AS (
	SELECT 
	PAYER.PAYER,
	PAYER.PAYEE,
	PAYER.MONEDA,
	SUM(PAYER.MONTO) AS MONTO_ENVIADO
	FROM DATA_TRX PAYER
	GROUP BY 
	PAYER.PAYER,
	PAYER.PAYEE,
	PAYER.MONEDA
),
PRE_FINAL AS (
	SELECT 
	P1.PAYER,
	P1.PAYEE,
	P1.MONEDA,
	COALESCE(SUM(P1.MONTO_ENVIADO),0) AS MONTO_ENVIADO,
	COALESCE(SUM(P2.MONTO_ENVIADO),0) AS MONTO_RECIBIDO
	FROM PRE_ENVIADO P1
	LEFT JOIN PRE_ENVIADO P2
	ON P1.PAYEE = P2.PAYER AND P1.PAYER = P2.PAYEE
	GROUP BY 
	P1.PAYER,
	P1.PAYEE,
	P1.MONEDA
)
SELECT 
REPLACE(REPLACE(ID1.ISSUER_CODE,'0144',''),'0231','') ORIGEN,
REPLACE(REPLACE(ID2.ISSUER_CODE,'0144',''),'0231','') DESTINO,
COALESCE(PF.MONTO_ENVIADO,0) AS MONTO_ENVIADO,
COALESCE(PF.MONTO_RECIBIDO,0) AS MONTO_RECIBIDO,
COALESCE((PF.MONTO_ENVIADO-PF.MONTO_RECIBIDO),0) AS MONTO_NETO,
COALESCE(PF.MONEDA,'PEN') AS MONEDA
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID1
INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID2 ON ID1.ISSUER_CODE <> ID2.ISSUER_CODE
LEFT JOIN PRE_FINAL PF ON REPLACE(REPLACE(ID1.ISSUER_CODE,'0144',''),'','') = PF.PAYER AND REPLACE(REPLACE(ID2.ISSUER_CODE,'0144',''),'','') = PF.PAYEE

[<src.core.models.report_row.ReportRow object at 0x7f0dde853490>, <src.core.models.report_row.ReportRow object at 0x7f0dde853370>, <src.core.models.report_row.ReportRow object at 0x7f0dde853520>, <src.core.models.report_row.ReportRow object at 0x7f0dde8532e0>, <src.core.models.report_row.ReportRow object at 0x7f0dde853280>, <src.core.models.report_row.ReportRow object at 0x7f0dde8534f0>, <src.core.models.report_row.ReportRow object at 0x7f0dde853340>, <src.core.models.report_row.ReportRow object at 0x7f0dde8533d0>, <src.core.models.report_row.ReportRow object at 0x7f0dde853400>, <src.core.models.report_row.ReportRow object at 0x7f0dde853580>, <src.core.models.report_row.ReportRow object at 0x7f0dde8535e0>, <src.core.models.report_row.ReportRow object at 0x7f0dde853640>, <src.core.models.report_row.ReportRow object at 0x7f0dde8536a0>, <src.core.models.report_row.ReportRow object at 0x7f0dde853700>, <src.core.models.report_row.ReportRow object at 0x7f0dde853760>, <src.core.models.report_row.ReportRow object at 0x7f0dde8537c0>, <src.core.models.report_row.ReportRow object at 0x7f0dde853820>, <src.core.models.report_row.ReportRow object at 0x7f0dde853880>, <src.core.models.report_row.ReportRow object at 0x7f0dde8538e0>, <src.core.models.report_row.ReportRow object at 0x7f0dde853940>, <src.core.models.report_row.ReportRow object at 0x7f0dde8539a0>, <src.core.models.report_row.ReportRow object at 0x7f0dde853a00>, <src.core.models.report_row.ReportRow object at 0x7f0dde853a60>, <src.core.models.report_row.ReportRow object at 0x7f0dde853ac0>, <src.core.models.report_row.ReportRow object at 0x7f0dde853b20>, <src.core.models.report_row.ReportRow object at 0x7f0dde853b80>, <src.core.models.report_row.ReportRow object at 0x7f0dde853be0>, <src.core.models.report_row.ReportRow object at 0x7f0dde853c40>, <src.core.models.report_row.ReportRow object at 0x7f0dde853ca0>, <src.core.models.report_row.ReportRow object at 0x7f0dde853d00>]
