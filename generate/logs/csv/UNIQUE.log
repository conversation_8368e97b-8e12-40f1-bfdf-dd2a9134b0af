WITH 
DATA_UNIQUE AS (
	SELECT
	"TransferID" AS trx_id,
	"TransferID" AS financial_trx_id,
	"ExternalTransactionID" AS external_trx_id,
	TO_CHAR("TransferDate", 'YYYY-MM-DD HH24:MI:SS') as datetime,
	"From_Msisdn" AS msisdn,
	"Remarks" ||'@'||REPLACE("To_Identifier",'1','') AS complete_username,
	LPAD(SUBSTR(TO_CHAR("Amount"), 1, LENGTH(TO_CHAR("Amount"))-2), LENGTH(TO_CHAR("Amount"))-2, '0') || '.' || SUBSTR(TO_CHAR("Amount"), LENGTH(TO_CHAR("Amount"))-1, 2) AS monto,
	"Amount" AS monto_real
	FROM USR_DATALAKE.PRE_LOG_TRX
	WHERE "TransactionType" = 'EXTERNAL_PAYMENT'
	AND "To_Identifier" LIKE '%unique%'
	AND TRUNC("TransferDate") = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
)
SELECT 
TRX_ID || ',' ||
FINANCIAL_TRX_ID || ',' ||
EXTERNAL_TRX_ID  || ',' ||
DATETIME  || ',' ||
MSISDN  || ',' ||
COMPLETE_USERNAME  || ',' ||
MONTO AS SALIDA
FROM DATA_UNIQUE
UNION ALL
SELECT 
'TOTAL UNIQUE, ' || 
LPAD(SUBSTR(TO_CHAR(SUM(MONTO_REAL)), 1, LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2, '0') || '.' || SUBSTR(TO_CHAR(SUM(MONTO_REAL)), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-1, 2)
FROM DATA_UNIQUE

Ejecutando query: WITH 
DATA_UNIQUE AS (
	SELECT
	"TransferID" AS trx_id,
	"TransferID" AS financial_trx_id,
	"ExternalTransactionID" AS external_trx_id,
	TO_CHAR("TransferDate", 'YYYY-MM-DD HH24:MI:SS') as datetime,
	"From_Msisdn" AS msisdn,
	"Remarks" ||'@'||REPLACE("To_Identifier",'1','') AS complete_username,
	LPAD(SUBSTR(TO_CHAR("Amount"), 1, LENGTH(TO_CHAR("Amount"))-2), LENGTH(TO_CHAR("Amount"))-2, '0') || '.' || SUBSTR(TO_CHAR("Amount"), LENGTH(TO_CHAR("Amount"))-1, 2) AS monto,
	"Amount" AS monto_real
	FROM USR_DATALAKE.PRE_LOG_TRX
	WHERE "TransactionType" = 'EXTERNAL_PAYMENT'
	AND "To_Identifier" LIKE '%unique%'
	AND TRUNC("TransferDate") = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))
)
SELECT 
TRX_ID || ',' ||
FINANCIAL_TRX_ID || ',' ||
EXTERNAL_TRX_ID  || ',' ||
DATETIME  || ',' ||
MSISDN  || ',' ||
COMPLETE_USERNAME  || ',' ||
MONTO AS SALIDA
FROM DATA_UNIQUE
UNION ALL
SELECT 
'TOTAL UNIQUE, ' || 
LPAD(SUBSTR(TO_CHAR(SUM(MONTO_REAL)), 1, LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2, '0') || '.' || SUBSTR(TO_CHAR(SUM(MONTO_REAL)), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-1, 2)
FROM DATA_UNIQUE

[<src.core.models.report_row.ReportRow object at 0x7f1c911c33d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c32b0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3460>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3250>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3220>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3430>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3280>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3310>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3340>, <src.core.models.report_row.ReportRow object at 0x7f1c911c34c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3550>, <src.core.models.report_row.ReportRow object at 0x7f1c911c35b0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3610>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3670>, <src.core.models.report_row.ReportRow object at 0x7f1c911c36d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3730>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3790>, <src.core.models.report_row.ReportRow object at 0x7f1c911c37f0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3850>, <src.core.models.report_row.ReportRow object at 0x7f1c911c38b0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3910>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3970>, <src.core.models.report_row.ReportRow object at 0x7f1c911c39d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3a30>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3a90>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3af0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3b50>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3bb0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3c10>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3c70>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3cd0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c32e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3f70>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3eb0>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3400>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3f10>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3e50>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd280>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd2e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd340>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd3a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd400>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd460>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd4c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd520>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd580>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd5e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd640>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd6a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd700>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd760>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd7c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd820>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd880>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd8e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd940>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd9a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cda00>, <src.core.models.report_row.ReportRow object at 0x7f1c911cda60>, <src.core.models.report_row.ReportRow object at 0x7f1c911cdac0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cdb20>, <src.core.models.report_row.ReportRow object at 0x7f1c911cdb80>, <src.core.models.report_row.ReportRow object at 0x7f1c911cdbe0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cdc40>, <src.core.models.report_row.ReportRow object at 0x7f1c911cdca0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cdd00>, <src.core.models.report_row.ReportRow object at 0x7f1c911cdd60>, <src.core.models.report_row.ReportRow object at 0x7f1c911cddc0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cde20>, <src.core.models.report_row.ReportRow object at 0x7f1c911cde80>, <src.core.models.report_row.ReportRow object at 0x7f1c911cdee0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cdf40>, <src.core.models.report_row.ReportRow object at 0x7f1c911cdfa0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd0d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd040>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd190>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd100>, <src.core.models.report_row.ReportRow object at 0x7f1c911c3d90>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd070>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1280>, <src.core.models.report_row.ReportRow object at 0x7f1c911d12e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1340>, <src.core.models.report_row.ReportRow object at 0x7f1c911d13a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1400>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1460>, <src.core.models.report_row.ReportRow object at 0x7f1c911d14c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1520>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1580>, <src.core.models.report_row.ReportRow object at 0x7f1c911d15e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1640>, <src.core.models.report_row.ReportRow object at 0x7f1c911d16a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1700>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1760>, <src.core.models.report_row.ReportRow object at 0x7f1c911d17c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1820>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1880>, <src.core.models.report_row.ReportRow object at 0x7f1c911d18e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1940>, <src.core.models.report_row.ReportRow object at 0x7f1c911d19a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1a00>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1a60>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1ac0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1b20>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1b80>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1be0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1c40>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1ca0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1d00>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1d60>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1dc0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1e20>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1e80>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1ee0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1f40>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1fa0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d10d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1040>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1190>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1100>, <src.core.models.report_row.ReportRow object at 0x7f1c911cd160>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1070>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5280>, <src.core.models.report_row.ReportRow object at 0x7f1c911d52e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5340>, <src.core.models.report_row.ReportRow object at 0x7f1c911d53a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5400>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5460>, <src.core.models.report_row.ReportRow object at 0x7f1c911d54c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5520>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5580>, <src.core.models.report_row.ReportRow object at 0x7f1c911d55e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5640>, <src.core.models.report_row.ReportRow object at 0x7f1c911d56a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5700>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5760>, <src.core.models.report_row.ReportRow object at 0x7f1c911d57c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5820>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5880>, <src.core.models.report_row.ReportRow object at 0x7f1c911d58e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5940>, <src.core.models.report_row.ReportRow object at 0x7f1c911d59a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5a00>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5a60>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5ac0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5b20>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5b80>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5be0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5c40>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5ca0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5d00>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5d60>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5dc0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5e20>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5e80>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5ee0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5f40>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5fa0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d50d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5040>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5190>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5100>, <src.core.models.report_row.ReportRow object at 0x7f1c911d1160>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5070>, <src.core.models.report_row.ReportRow object at 0x7f1c911da280>, <src.core.models.report_row.ReportRow object at 0x7f1c911da2e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911da340>, <src.core.models.report_row.ReportRow object at 0x7f1c911da3a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911da400>, <src.core.models.report_row.ReportRow object at 0x7f1c911da460>, <src.core.models.report_row.ReportRow object at 0x7f1c911da4c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911da520>, <src.core.models.report_row.ReportRow object at 0x7f1c911da580>, <src.core.models.report_row.ReportRow object at 0x7f1c911da5e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911da640>, <src.core.models.report_row.ReportRow object at 0x7f1c911da6a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911da700>, <src.core.models.report_row.ReportRow object at 0x7f1c911da760>, <src.core.models.report_row.ReportRow object at 0x7f1c911da7c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911da820>, <src.core.models.report_row.ReportRow object at 0x7f1c911da880>, <src.core.models.report_row.ReportRow object at 0x7f1c911da8e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911da940>, <src.core.models.report_row.ReportRow object at 0x7f1c911da9a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911daa00>, <src.core.models.report_row.ReportRow object at 0x7f1c911daa60>, <src.core.models.report_row.ReportRow object at 0x7f1c911daac0>, <src.core.models.report_row.ReportRow object at 0x7f1c911dab20>, <src.core.models.report_row.ReportRow object at 0x7f1c911dab80>, <src.core.models.report_row.ReportRow object at 0x7f1c911dabe0>, <src.core.models.report_row.ReportRow object at 0x7f1c911dac40>, <src.core.models.report_row.ReportRow object at 0x7f1c911daca0>, <src.core.models.report_row.ReportRow object at 0x7f1c911dad00>, <src.core.models.report_row.ReportRow object at 0x7f1c911dad60>, <src.core.models.report_row.ReportRow object at 0x7f1c911dadc0>, <src.core.models.report_row.ReportRow object at 0x7f1c911dae20>, <src.core.models.report_row.ReportRow object at 0x7f1c911dae80>, <src.core.models.report_row.ReportRow object at 0x7f1c911daee0>, <src.core.models.report_row.ReportRow object at 0x7f1c911daf40>, <src.core.models.report_row.ReportRow object at 0x7f1c911dafa0>, <src.core.models.report_row.ReportRow object at 0x7f1c911da0d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911da040>, <src.core.models.report_row.ReportRow object at 0x7f1c911da190>, <src.core.models.report_row.ReportRow object at 0x7f1c911da100>, <src.core.models.report_row.ReportRow object at 0x7f1c911d5160>, <src.core.models.report_row.ReportRow object at 0x7f1c911da070>, <src.core.models.report_row.ReportRow object at 0x7f1c911de280>, <src.core.models.report_row.ReportRow object at 0x7f1c911de2e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911de340>, <src.core.models.report_row.ReportRow object at 0x7f1c911de3a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911de400>, <src.core.models.report_row.ReportRow object at 0x7f1c911de460>, <src.core.models.report_row.ReportRow object at 0x7f1c911de4c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911de520>, <src.core.models.report_row.ReportRow object at 0x7f1c911de580>, <src.core.models.report_row.ReportRow object at 0x7f1c911de5e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911de640>, <src.core.models.report_row.ReportRow object at 0x7f1c911de6a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911de700>, <src.core.models.report_row.ReportRow object at 0x7f1c911de760>, <src.core.models.report_row.ReportRow object at 0x7f1c911de7c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911de820>, <src.core.models.report_row.ReportRow object at 0x7f1c911de880>, <src.core.models.report_row.ReportRow object at 0x7f1c911de8e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911de940>, <src.core.models.report_row.ReportRow object at 0x7f1c911de9a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911dea00>, <src.core.models.report_row.ReportRow object at 0x7f1c911dea60>, <src.core.models.report_row.ReportRow object at 0x7f1c911deac0>, <src.core.models.report_row.ReportRow object at 0x7f1c911deb20>, <src.core.models.report_row.ReportRow object at 0x7f1c911deb80>, <src.core.models.report_row.ReportRow object at 0x7f1c911debe0>, <src.core.models.report_row.ReportRow object at 0x7f1c911dec40>, <src.core.models.report_row.ReportRow object at 0x7f1c911deca0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ded00>, <src.core.models.report_row.ReportRow object at 0x7f1c911ded60>, <src.core.models.report_row.ReportRow object at 0x7f1c911dedc0>, <src.core.models.report_row.ReportRow object at 0x7f1c911dee20>, <src.core.models.report_row.ReportRow object at 0x7f1c911dee80>, <src.core.models.report_row.ReportRow object at 0x7f1c911deee0>, <src.core.models.report_row.ReportRow object at 0x7f1c911def40>, <src.core.models.report_row.ReportRow object at 0x7f1c911defa0>, <src.core.models.report_row.ReportRow object at 0x7f1c911de0d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911de040>, <src.core.models.report_row.ReportRow object at 0x7f1c911de190>, <src.core.models.report_row.ReportRow object at 0x7f1c911de100>, <src.core.models.report_row.ReportRow object at 0x7f1c911da160>, <src.core.models.report_row.ReportRow object at 0x7f1c911de070>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1280>, <src.core.models.report_row.ReportRow object at 0x7f1c911e12e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1340>, <src.core.models.report_row.ReportRow object at 0x7f1c911e13a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1400>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1460>, <src.core.models.report_row.ReportRow object at 0x7f1c911e14c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1520>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1580>, <src.core.models.report_row.ReportRow object at 0x7f1c911e15e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1640>, <src.core.models.report_row.ReportRow object at 0x7f1c911e16a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1700>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1760>, <src.core.models.report_row.ReportRow object at 0x7f1c911e17c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1820>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1880>, <src.core.models.report_row.ReportRow object at 0x7f1c911e18e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1940>, <src.core.models.report_row.ReportRow object at 0x7f1c911e19a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1a00>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1a60>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1ac0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1b20>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1b80>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1be0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1c40>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1ca0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1d00>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1d60>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1dc0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1e20>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1e80>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1ee0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1f40>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1fa0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e10d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1040>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1190>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1100>, <src.core.models.report_row.ReportRow object at 0x7f1c911de160>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1070>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6280>, <src.core.models.report_row.ReportRow object at 0x7f1c911e62e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6340>, <src.core.models.report_row.ReportRow object at 0x7f1c911e63a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6400>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6460>, <src.core.models.report_row.ReportRow object at 0x7f1c911e64c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6520>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6580>, <src.core.models.report_row.ReportRow object at 0x7f1c911e65e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6640>, <src.core.models.report_row.ReportRow object at 0x7f1c911e66a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6700>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6760>, <src.core.models.report_row.ReportRow object at 0x7f1c911e67c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6820>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6880>, <src.core.models.report_row.ReportRow object at 0x7f1c911e68e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6940>, <src.core.models.report_row.ReportRow object at 0x7f1c911e69a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6a00>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6a60>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6ac0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6b20>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6b80>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6be0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6c40>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6ca0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6d00>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6d60>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6dc0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6e20>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6e80>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6ee0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6f40>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6fa0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e60d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6040>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6190>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6100>, <src.core.models.report_row.ReportRow object at 0x7f1c911e1160>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6070>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea280>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea2e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea340>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea3a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea400>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea460>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea4c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea520>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea580>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea5e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea640>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea6a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea700>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea760>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea7c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea820>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea880>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea8e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea940>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea9a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911eaa00>, <src.core.models.report_row.ReportRow object at 0x7f1c911eaa60>, <src.core.models.report_row.ReportRow object at 0x7f1c911eaac0>, <src.core.models.report_row.ReportRow object at 0x7f1c911eab20>, <src.core.models.report_row.ReportRow object at 0x7f1c911eab80>, <src.core.models.report_row.ReportRow object at 0x7f1c911eabe0>, <src.core.models.report_row.ReportRow object at 0x7f1c911eac40>, <src.core.models.report_row.ReportRow object at 0x7f1c911eaca0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ead00>, <src.core.models.report_row.ReportRow object at 0x7f1c911ead60>, <src.core.models.report_row.ReportRow object at 0x7f1c911eadc0>, <src.core.models.report_row.ReportRow object at 0x7f1c911eae20>, <src.core.models.report_row.ReportRow object at 0x7f1c911eae80>, <src.core.models.report_row.ReportRow object at 0x7f1c911eaee0>, <src.core.models.report_row.ReportRow object at 0x7f1c911eaf40>, <src.core.models.report_row.ReportRow object at 0x7f1c911eafa0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea0d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea040>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea190>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea100>, <src.core.models.report_row.ReportRow object at 0x7f1c911e6160>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea070>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee280>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee2e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee340>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee3a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee400>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee460>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee4c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee520>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee580>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee5e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee640>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee6a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee700>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee760>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee7c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee820>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee880>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee8e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee940>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee9a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911eea00>, <src.core.models.report_row.ReportRow object at 0x7f1c911eea60>, <src.core.models.report_row.ReportRow object at 0x7f1c911eeac0>, <src.core.models.report_row.ReportRow object at 0x7f1c911eeb20>, <src.core.models.report_row.ReportRow object at 0x7f1c911eeb80>, <src.core.models.report_row.ReportRow object at 0x7f1c911eebe0>, <src.core.models.report_row.ReportRow object at 0x7f1c911eec40>, <src.core.models.report_row.ReportRow object at 0x7f1c911eeca0>, <src.core.models.report_row.ReportRow object at 0x7f1c911eed00>, <src.core.models.report_row.ReportRow object at 0x7f1c911eed60>, <src.core.models.report_row.ReportRow object at 0x7f1c911eedc0>, <src.core.models.report_row.ReportRow object at 0x7f1c911eee20>, <src.core.models.report_row.ReportRow object at 0x7f1c911eee80>, <src.core.models.report_row.ReportRow object at 0x7f1c911eeee0>, <src.core.models.report_row.ReportRow object at 0x7f1c911eef40>, <src.core.models.report_row.ReportRow object at 0x7f1c911eefa0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee0d0>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee040>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee190>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee100>, <src.core.models.report_row.ReportRow object at 0x7f1c911ea160>, <src.core.models.report_row.ReportRow object at 0x7f1c911ee070>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3280>, <src.core.models.report_row.ReportRow object at 0x7f1c911f32e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3340>, <src.core.models.report_row.ReportRow object at 0x7f1c911f33a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3400>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3460>, <src.core.models.report_row.ReportRow object at 0x7f1c911f34c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3520>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3580>, <src.core.models.report_row.ReportRow object at 0x7f1c911f35e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3640>, <src.core.models.report_row.ReportRow object at 0x7f1c911f36a0>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3700>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3760>, <src.core.models.report_row.ReportRow object at 0x7f1c911f37c0>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3820>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3880>, <src.core.models.report_row.ReportRow object at 0x7f1c911f38e0>, <src.core.models.report_row.ReportRow object at 0x7f1c911f3940>, <src.core.models.report_row.ReportRow object at 0x7f1c911f39a0>]2025-06-13 06:41:23,196 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-06-13 06:41:23,199 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/PDP-REPORTE-UNIQUE-20250613000000.csv
2025-06-13 06:41:23,211 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-13 06:41:23,380 - root - INFO - Archivo: PDP-REPORTE-UNIQUE-20250613000000.csv subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-06-13/UNIQUE/PDP-REPORTE-UNIQUE-20250613000000.csv

Archivo guardado correctamente en /home/<USER>/output/csv/PDP-REPORTE-UNIQUE-20250613000000.csv
