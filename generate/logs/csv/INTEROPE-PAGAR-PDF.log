2025-06-12 14:54:53,065 - root - INFO - No se encontraron datos para exportar.
2025-06-12 14:54:53,067 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-FCOMPARTAMOS-PAGAR-202506-PDF.csv
2025-06-12 14:55:04,722 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-06-12 14:55:04,723 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-BNACION-PAGAR-202506-PDF.csv
FCOMPARTAMOS
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCOMPARTAMOS' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FCOMPARTAMOS' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCOMPARTAMOS' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FCOMPARTAMOS' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

No se encontraron registros en la consulta.
[]
BNACION
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'BNACION' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'BNACION' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'BNACION' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'BNACION' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

[<src.core.models.report_row.ReportRow object at 0x7f8b11bd8370>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd8250>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd8490>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd84f0>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd8550>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd85b0>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd8610>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd8670>]
CRANDES
2025-06-12 14:55:15,747 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-06-12 14:55:15,748 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-CRANDES-PAGAR-202506-PDF.csv
2025-06-12 14:55:26,113 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-06-12 14:55:26,114 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-CCUSCO-PAGAR-202506-PDF.csv
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CRANDES' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'CRANDES' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CRANDES' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'CRANDES' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

[<src.core.models.report_row.ReportRow object at 0x7f8b11bd8610>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd84f0>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd81f0>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd8430>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd8790>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd87f0>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd8400>]
CCUSCO
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CCUSCO' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'CCUSCO' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CCUSCO' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'CCUSCO' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

[<src.core.models.report_row.ReportRow object at 0x7f8b11bd8220>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd87f0>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd82e0>, <src.core.models.report_row.ReportRow object at 0x7f8b11bd8700>]
FCONFIANZA
2025-06-12 14:55:36,469 - root - INFO - No se encontraron datos para exportar.
2025-06-12 14:55:36,470 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-0231FCONFIANZA-PAGAR-202506-PDF.csv
2025-06-12 14:55:47,344 - root - INFO - No se encontraron datos para exportar.
2025-06-12 14:55:47,344 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-FQAPAQ-PAGAR-202506-PDF.csv
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCONFIANZA' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FCONFIANZA' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCONFIANZA' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FCONFIANZA' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

No se encontraron registros en la consulta.
[]
FQAPAQ
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FQAPAQ' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FQAPAQ' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FQAPAQ' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FQAPAQ' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

No se encontraron registros en la consulta.
[]
