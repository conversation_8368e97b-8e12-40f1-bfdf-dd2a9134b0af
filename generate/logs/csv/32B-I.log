WITH
PRE_DATA_TRX_FROM AS (
SELECT 
	TL."Amount" TRANSFER_VALUE,
	TL."TransferID" FIELD7,
	TL."FromID_Mobiquity" AS USER_ID,
	TL."TransactionType" TRX_SERVICE,
	TL."From_Grade" AS GRADE,
	TL."From_BankDomain" AS ISSUER_CODE,
	TL."Currency" PROVIDER_NAME
FROM USR_DATALAKE.PRE_LOG_TRX TL
WHERE TRUNC(TL."TransferDate") <= TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS') 
),
PRE_DATA_TRX_TO AS (
SELECT 
	TL."Amount" TRANSFER_VALUE,
	TL."TransferID" FIELD7,
	TL."ToID_Mobiquity" AS USER_ID,
	TL."TransactionType" TRX_SERVICE,
	TL."To_Grade" AS GRADE,
	TL."To_BankDomain" AS ISSUER_CODE,
	TL."Currency" PROVIDER_NAME
FROM USR_DATALAKE.PRE_LOG_TRX TL
WHERE TRUNC("TransferDate") <= TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')
),
PRE_DATA_TRX AS (
SELECT * FROM PRE_DATA_TRX_FROM WHERE TRX_SERVICE NOT IN ('CASH_IN','DEPOSIT','BATCH_TRANSFER','REVERSAL')
UNION
SELECT * FROM PRE_DATA_TRX_TO WHERE TRX_SERVICE IN ('CASH_IN','DEPOSIT','BATCH_TRANSFER','REVERSAL')
),
DATA_TRX AS (
	SELECT 
		UP.USER_ID,
		KD.ID_TYPE AS TDOCUMENTO,
		MTH.TRANSFER_VALUE,
		MTH.FIELD7,
		MTH.ISSUER_CODE AS EMISOR,
		CASE 
			WHEN MTH.PROVIDER_NAME='Soles' OR MTH.PROVIDER_NAME IS null THEN 'PEN' 
			ELSE 'OTHER' 
		END AS MONEDA,
		CASE 
			WHEN UPPER(MTH.GRADE) = 'NORMAL ACCOUNT PROFILE' THEN 'SIMPLIFICADA'
			WHEN UPPER(MTH.GRADE) = 'NORMAL GENERAL ACCOUNT PROFILE' THEN 'GENERAL'
			ELSE ''
		END AS TIPO_CUENTA,
		CASE 
			WHEN MTH.TRX_SERVICE IN ('CASH_IN','DEPOSIT') AND MTH.GRADE IN ('NORMAL ACCOUNT PROFILE','NORMAL GENERAL ACCOUNT PROFILE') THEN 'CONVERSION'
			WHEN MTH.TRX_SERVICE IN ('TRANSFER','EXTERNAL_PAYMENT','PAYMENT','BATCH_TRANSFER','REVERSAL_BATCH_TRANSFER')  AND MTH.GRADE IN ('NORMAL ACCOUNT PROFILE','NORMAL GENERAL ACCOUNT PROFILE') THEN 'TRANSFERENCIAS'
			WHEN MTH.TRX_SERVICE IN ('CASH_OUT','TRANSFER_TO_ANY_BANK_ACCOUNT','CASH_OUT_ATM')  AND MTH.GRADE IN ('NORMAL ACCOUNT PROFILE','NORMAL GENERAL ACCOUNT PROFILE') THEN 'RECONVERSION'
			WHEN MTH.TRX_SERVICE IN ('REVERSAL','ADJUSTMENT')  AND MTH.GRADE IN ('NORMAL ACCOUNT PROFILE','NORMAL GENERAL ACCOUNT PROFILE') THEN 'OTROS'
			ELSE ''
		END AS TIPO_MOVIMIENTO
	FROM PRE_DATA_TRX MTH
	INNER JOIN PDP_PROD10_MAINDB.USER_PROFILE UP ON MTH.USER_ID = UP.USER_ID
	INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KD ON UP.KYC_ID = KD.KYC_ID
),
TRX_ACUMULADO AS (
SELECT 
	DT.USER_ID,
	DT.TDOCUMENTO,
	DT.MONEDA,
	DT.TIPO_CUENTA,
	DT.EMISOR,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'CONVERSION' THEN DT.TRANSFER_VALUE
		ELSE 0 
	END AS MONTO_CONVERSION,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'TRANSFERENCIAS' THEN DT.TRANSFER_VALUE 
		ELSE 0 
	END AS MONTO_TRANSFERENCIAS,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'RECONVERSION' THEN DT.TRANSFER_VALUE 
		ELSE 0
	END AS MONTO_RECONVERSION,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'OTROS' THEN DT.TRANSFER_VALUE
		ELSE 0 
	END AS MONTO_OTROS,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'CONVERSION' THEN 1
		ELSE 0 
	END AS CNT_CONVERSION,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'TRANSFERENCIAS' THEN 1
		ELSE 0 
	END AS CNT_TRANSFERENCIAS,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'RECONVERSION' THEN 1 
		ELSE 0 
	END AS CNT_RECONVERSION,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'OTROS' THEN 1 
		ELSE 0
	END AS CNT_OTROS
FROM DATA_TRX DT
WHERE TIPO_CUENTA IS NOT NULL
AND TIPO_MOVIMIENTO IS NOT NULL
),
DATA_AGRUPADOS AS (
SELECT 
	DT.USER_ID,
	DT.TDOCUMENTO,
	DT.MONEDA,
	DT.TIPO_CUENTA,
	DT.EMISOR,
	COALESCE(SUM(MONTO_CONVERSION),0) AS TOTAL_CONVERSION,
	COALESCE(SUM(MONTO_TRANSFERENCIAS),0) AS TOTAL_TRANSFERENCIAS,
	COALESCE(SUM(MONTO_RECONVERSION),0) AS TOTAL_RECONVERSION,
	COALESCE(SUM(MONTO_OTROS),0) AS TOTAL_OTROS,
	COALESCE(SUM(CNT_CONVERSION),0) AS CNT_CONVERSIONES,
	COALESCE(SUM(CNT_TRANSFERENCIAS),0) AS CNT_TRANSFERENCIAS,
	COALESCE(SUM(CNT_RECONVERSION),0) AS CNT_RECONVERSION,
	COALESCE(SUM(CNT_OTROS),0) AS CNT_OTROS
FROM TRX_ACUMULADO DT
GROUP BY 
	DT.USER_ID,
	DT.TDOCUMENTO,
	DT.MONEDA,
	DT.TIPO_CUENTA,
	DT.EMISOR
),
DATA_FINAL AS (
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1000' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1100' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1200' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS DA  WHERE TIPO_CUENTA = 'SIMPLIFICADA' AND TDOCUMENTO IN ('DNI','CE','OTHER') AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1300' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('DNI','CE','OTHER') AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1400' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TDOCUMENTO IN ('DNI','CE','OTHER') AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1500' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1600' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('DNI','CE','OTHER') AND MONEDA <> 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1650' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1700' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'SIMPLIFICADA' AND TDOCUMENTO IN ('DNI','CE','OTHER')) DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1800' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('DNI','CE','OTHER')) DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1900' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TDOCUMENTO IN ('DNI','CE','OTHER')) DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2000' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2100' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2200' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('RUC') AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2300' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2400' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('RUC') AND MONEDA <> 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2500' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('RUC')) DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3000' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3100' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 CNT_CONVERSION,0 CNT_TRANSFERNCIAS,0 CNT_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3200' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'SIMPLIFICADA' AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3300' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3400' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3500' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3600' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND MONEDA <> 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3650' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3700' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'SIMPLIFICADA') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3800' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3900' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS) DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '4000' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
)
SELECT
	REPLACE(REPLACE(EMISOR,'0144',''),'0104') AS EMISOR,
	LPAD(CODIGO,6,'0') || 
	LPAD(CONVERSION,18,'0') || 
	LPAD(C_CONVERSION,18,'0') || 
	LPAD(TRANSFERENCIAS,18,'0') || 
	LPAD(C_TRANSFERENCIAS,18,'0') ||
	LPAD(RECONVERSION,18,'0') || 
	LPAD(C_RECONVERSION,18,'0') ||
	LPAD(OTROS,18,'0') || 
	LPAD(C_OTROS,18,'0') ||
	LPAD('0',6,'0') AS TRAMA
FROM DATA_FINAL


--EJECUTAR Y GUARDAR CON EL NOMBRE: 32B-I-YYYYMMDD.csv (********)

Ejecutando query: WITH
PRE_DATA_TRX_FROM AS (
SELECT 
	TL."Amount" TRANSFER_VALUE,
	TL."TransferID" FIELD7,
	TL."FromID_Mobiquity" AS USER_ID,
	TL."TransactionType" TRX_SERVICE,
	TL."From_Grade" AS GRADE,
	TL."From_BankDomain" AS ISSUER_CODE,
	TL."Currency" PROVIDER_NAME
FROM USR_DATALAKE.PRE_LOG_TRX TL
WHERE TRUNC(TL."TransferDate") <= TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS') 
),
PRE_DATA_TRX_TO AS (
SELECT 
	TL."Amount" TRANSFER_VALUE,
	TL."TransferID" FIELD7,
	TL."ToID_Mobiquity" AS USER_ID,
	TL."TransactionType" TRX_SERVICE,
	TL."To_Grade" AS GRADE,
	TL."To_BankDomain" AS ISSUER_CODE,
	TL."Currency" PROVIDER_NAME
FROM USR_DATALAKE.PRE_LOG_TRX TL
WHERE TRUNC("TransferDate") <= TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')
),
PRE_DATA_TRX AS (
SELECT * FROM PRE_DATA_TRX_FROM WHERE TRX_SERVICE NOT IN ('CASH_IN','DEPOSIT','BATCH_TRANSFER','REVERSAL')
UNION
SELECT * FROM PRE_DATA_TRX_TO WHERE TRX_SERVICE IN ('CASH_IN','DEPOSIT','BATCH_TRANSFER','REVERSAL')
),
DATA_TRX AS (
	SELECT 
		UP.USER_ID,
		KD.ID_TYPE AS TDOCUMENTO,
		MTH.TRANSFER_VALUE,
		MTH.FIELD7,
		MTH.ISSUER_CODE AS EMISOR,
		CASE 
			WHEN MTH.PROVIDER_NAME='Soles' OR MTH.PROVIDER_NAME IS null THEN 'PEN' 
			ELSE 'OTHER' 
		END AS MONEDA,
		CASE 
			WHEN UPPER(MTH.GRADE) = 'NORMAL ACCOUNT PROFILE' THEN 'SIMPLIFICADA'
			WHEN UPPER(MTH.GRADE) = 'NORMAL GENERAL ACCOUNT PROFILE' THEN 'GENERAL'
			ELSE ''
		END AS TIPO_CUENTA,
		CASE 
			WHEN MTH.TRX_SERVICE IN ('CASH_IN','DEPOSIT') AND MTH.GRADE IN ('NORMAL ACCOUNT PROFILE','NORMAL GENERAL ACCOUNT PROFILE') THEN 'CONVERSION'
			WHEN MTH.TRX_SERVICE IN ('TRANSFER','EXTERNAL_PAYMENT','PAYMENT','BATCH_TRANSFER','REVERSAL_BATCH_TRANSFER')  AND MTH.GRADE IN ('NORMAL ACCOUNT PROFILE','NORMAL GENERAL ACCOUNT PROFILE') THEN 'TRANSFERENCIAS'
			WHEN MTH.TRX_SERVICE IN ('CASH_OUT','TRANSFER_TO_ANY_BANK_ACCOUNT','CASH_OUT_ATM')  AND MTH.GRADE IN ('NORMAL ACCOUNT PROFILE','NORMAL GENERAL ACCOUNT PROFILE') THEN 'RECONVERSION'
			WHEN MTH.TRX_SERVICE IN ('REVERSAL','ADJUSTMENT')  AND MTH.GRADE IN ('NORMAL ACCOUNT PROFILE','NORMAL GENERAL ACCOUNT PROFILE') THEN 'OTROS'
			ELSE ''
		END AS TIPO_MOVIMIENTO
	FROM PRE_DATA_TRX MTH
	INNER JOIN PDP_PROD10_MAINDB.USER_PROFILE UP ON MTH.USER_ID = UP.USER_ID
	INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KD ON UP.KYC_ID = KD.KYC_ID
),
TRX_ACUMULADO AS (
SELECT 
	DT.USER_ID,
	DT.TDOCUMENTO,
	DT.MONEDA,
	DT.TIPO_CUENTA,
	DT.EMISOR,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'CONVERSION' THEN DT.TRANSFER_VALUE
		ELSE 0 
	END AS MONTO_CONVERSION,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'TRANSFERENCIAS' THEN DT.TRANSFER_VALUE 
		ELSE 0 
	END AS MONTO_TRANSFERENCIAS,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'RECONVERSION' THEN DT.TRANSFER_VALUE 
		ELSE 0
	END AS MONTO_RECONVERSION,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'OTROS' THEN DT.TRANSFER_VALUE
		ELSE 0 
	END AS MONTO_OTROS,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'CONVERSION' THEN 1
		ELSE 0 
	END AS CNT_CONVERSION,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'TRANSFERENCIAS' THEN 1
		ELSE 0 
	END AS CNT_TRANSFERENCIAS,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'RECONVERSION' THEN 1 
		ELSE 0 
	END AS CNT_RECONVERSION,
	CASE 
		WHEN TIPO_MOVIMIENTO = 'OTROS' THEN 1 
		ELSE 0
	END AS CNT_OTROS
FROM DATA_TRX DT
WHERE TIPO_CUENTA IS NOT NULL
AND TIPO_MOVIMIENTO IS NOT NULL
),
DATA_AGRUPADOS AS (
SELECT 
	DT.USER_ID,
	DT.TDOCUMENTO,
	DT.MONEDA,
	DT.TIPO_CUENTA,
	DT.EMISOR,
	COALESCE(SUM(MONTO_CONVERSION),0) AS TOTAL_CONVERSION,
	COALESCE(SUM(MONTO_TRANSFERENCIAS),0) AS TOTAL_TRANSFERENCIAS,
	COALESCE(SUM(MONTO_RECONVERSION),0) AS TOTAL_RECONVERSION,
	COALESCE(SUM(MONTO_OTROS),0) AS TOTAL_OTROS,
	COALESCE(SUM(CNT_CONVERSION),0) AS CNT_CONVERSIONES,
	COALESCE(SUM(CNT_TRANSFERENCIAS),0) AS CNT_TRANSFERENCIAS,
	COALESCE(SUM(CNT_RECONVERSION),0) AS CNT_RECONVERSION,
	COALESCE(SUM(CNT_OTROS),0) AS CNT_OTROS
FROM TRX_ACUMULADO DT
GROUP BY 
	DT.USER_ID,
	DT.TDOCUMENTO,
	DT.MONEDA,
	DT.TIPO_CUENTA,
	DT.EMISOR
),
DATA_FINAL AS (
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1000' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1100' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1200' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS DA  WHERE TIPO_CUENTA = 'SIMPLIFICADA' AND TDOCUMENTO IN ('DNI','CE','OTHER') AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1300' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('DNI','CE','OTHER') AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1400' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TDOCUMENTO IN ('DNI','CE','OTHER') AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1500' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1600' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('DNI','CE','OTHER') AND MONEDA <> 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1650' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1700' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'SIMPLIFICADA' AND TDOCUMENTO IN ('DNI','CE','OTHER')) DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1800' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('DNI','CE','OTHER')) DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '1900' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TDOCUMENTO IN ('DNI','CE','OTHER')) DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2000' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2100' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2200' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('RUC') AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2300' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2400' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('RUC') AND MONEDA <> 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '2500' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND TDOCUMENTO IN ('RUC')) DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3000' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3100' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 CNT_CONVERSION,0 CNT_TRANSFERNCIAS,0 CNT_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3200' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'SIMPLIFICADA' AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3300' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3400' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE MONEDA = 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3500' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3600' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL' AND MONEDA <> 'PEN') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3650' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3700' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'SIMPLIFICADA') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3800' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS WHERE TIPO_CUENTA = 'GENERAL') DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '3900' CODIGO, COALESCE(SUM(TOTAL_CONVERSION),0) CONVERSION, COALESCE(SUM(TOTAL_TRANSFERENCIAS),0) TRANSFERENCIAS, COALESCE(SUM(TOTAL_RECONVERSION),0) RECONVERSION, COALESCE(SUM(TOTAL_OTROS),0) OTROS, COALESCE(SUM(CNT_CONVERSIONES),0) C_CONVERSION, COALESCE(SUM(CNT_TRANSFERENCIAS),0) C_TRANSFERENCIAS, COALESCE(SUM(CNT_RECONVERSION),0) C_RECONVERSION, COALESCE(SUM(CNT_OTROS),0) C_OTROS FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN (SELECT * FROM DATA_AGRUPADOS) DA ON REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') = DA.EMISOR GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
UNION 
SELECT REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','') EMISOR, '4000' CODIGO, 0 CONVERSION,0 TRANSFERENCIAS,0 RECONVERSION,0 OTROS,0 C_CONVERSION,0 C_TRANSFERENCIAS,0 C_RECONVERSION,0 C_OTROS  FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID GROUP BY REPLACE(REPLACE(ID.ISSUER_CODE,'',''),'0144','')
)
SELECT
	REPLACE(REPLACE(EMISOR,'0144',''),'0104') AS EMISOR,
	LPAD(CODIGO,6,'0') || 
	LPAD(CONVERSION,18,'0') || 
	LPAD(C_CONVERSION,18,'0') || 
	LPAD(TRANSFERENCIAS,18,'0') || 
	LPAD(C_TRANSFERENCIAS,18,'0') ||
	LPAD(RECONVERSION,18,'0') || 
	LPAD(C_RECONVERSION,18,'0') ||
	LPAD(OTROS,18,'0') || 
	LPAD(C_OTROS,18,'0') ||
	LPAD('0',6,'0') AS TRAMA
FROM DATA_FINAL


--EJECUTAR Y GUARDAR CON EL NOMBRE: 32B-I-YYYYMMDD.csv (********)

[<src.core.models.report_row.ReportRow object at 0x7f4c3c218730>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218610>, <src.core.models.report_row.ReportRow object at 0x7f4c3c2187c0>, <src.core.models.report_row.ReportRow object at 0x7f4c3c2185b0>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218580>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218790>, <src.core.models.report_row.ReportRow object at 0x7f4c3c2185e0>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218670>, <src.core.models.report_row.ReportRow object at 0x7f4c3c2186a0>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218820>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218880>, <src.core.models.report_row.ReportRow object at 0x7f4c3c2188e0>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218940>, <src.core.models.report_row.ReportRow object at 0x7f4c3c2189a0>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218a00>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218a60>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218ac0>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218b20>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218b80>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218be0>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218c40>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218ca0>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218d00>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218f40>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218fa0>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218e80>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218640>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218e20>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5250>, <src.core.models.report_row.ReportRow object at 0x7f4c403c52b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5310>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5370>, <src.core.models.report_row.ReportRow object at 0x7f4c403c53d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5430>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5490>, <src.core.models.report_row.ReportRow object at 0x7f4c403c54f0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5550>, <src.core.models.report_row.ReportRow object at 0x7f4c403c55b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5610>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5670>, <src.core.models.report_row.ReportRow object at 0x7f4c403c56d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5730>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5790>, <src.core.models.report_row.ReportRow object at 0x7f4c403c57f0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5850>, <src.core.models.report_row.ReportRow object at 0x7f4c403c58b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5910>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5970>, <src.core.models.report_row.ReportRow object at 0x7f4c403c59d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5a30>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5a90>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5af0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5b50>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5bb0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5c10>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5c70>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5cd0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5d30>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5d90>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5df0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5e50>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5eb0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5f10>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5f70>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5fd0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c50a0>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5070>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5160>, <src.core.models.report_row.ReportRow object at 0x7f4c3c218d60>, <src.core.models.report_row.ReportRow object at 0x7f4c403c5040>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca250>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca2b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca310>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca370>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca3d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca430>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca490>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca4f0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca550>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca5b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca610>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca670>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca6d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca730>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca790>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca7f0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca850>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca8b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca910>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca970>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca9d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403caa30>, <src.core.models.report_row.ReportRow object at 0x7f4c403caa90>, <src.core.models.report_row.ReportRow object at 0x7f4c403caaf0>, <src.core.models.report_row.ReportRow object at 0x7f4c403cab50>, <src.core.models.report_row.ReportRow object at 0x7f4c403cabb0>, <src.core.models.report_row.ReportRow object at 0x7f4c403cac10>, <src.core.models.report_row.ReportRow object at 0x7f4c403cac70>, <src.core.models.report_row.ReportRow object at 0x7f4c403cacd0>, <src.core.models.report_row.ReportRow object at 0x7f4c403cad30>, <src.core.models.report_row.ReportRow object at 0x7f4c403cad90>, <src.core.models.report_row.ReportRow object at 0x7f4c403cadf0>, <src.core.models.report_row.ReportRow object at 0x7f4c403cae50>, <src.core.models.report_row.ReportRow object at 0x7f4c403caeb0>, <src.core.models.report_row.ReportRow object at 0x7f4c403caf10>, <src.core.models.report_row.ReportRow object at 0x7f4c403caf70>, <src.core.models.report_row.ReportRow object at 0x7f4c403cafd0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca0a0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca070>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca160>, <src.core.models.report_row.ReportRow object at 0x7f4c403c50d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca040>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce250>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce2b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce310>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce370>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce3d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce430>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce490>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce4f0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce550>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce5b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce610>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce670>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce6d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce730>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce790>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce7f0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce850>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce8b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce910>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce970>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce9d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403cea30>, <src.core.models.report_row.ReportRow object at 0x7f4c403cea90>, <src.core.models.report_row.ReportRow object at 0x7f4c403ceaf0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ceb50>, <src.core.models.report_row.ReportRow object at 0x7f4c403cebb0>, <src.core.models.report_row.ReportRow object at 0x7f4c403cec10>, <src.core.models.report_row.ReportRow object at 0x7f4c403cec70>, <src.core.models.report_row.ReportRow object at 0x7f4c403cecd0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ced30>, <src.core.models.report_row.ReportRow object at 0x7f4c403ced90>, <src.core.models.report_row.ReportRow object at 0x7f4c403cedf0>, <src.core.models.report_row.ReportRow object at 0x7f4c403cee50>, <src.core.models.report_row.ReportRow object at 0x7f4c403ceeb0>, <src.core.models.report_row.ReportRow object at 0x7f4c403cef10>, <src.core.models.report_row.ReportRow object at 0x7f4c403cef70>, <src.core.models.report_row.ReportRow object at 0x7f4c403cefd0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce0a0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce070>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce160>, <src.core.models.report_row.ReportRow object at 0x7f4c403ca0d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403ce040>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1250>, <src.core.models.report_row.ReportRow object at 0x7f4c403d12b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1310>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1370>, <src.core.models.report_row.ReportRow object at 0x7f4c403d13d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1430>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1490>, <src.core.models.report_row.ReportRow object at 0x7f4c403d14f0>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1550>, <src.core.models.report_row.ReportRow object at 0x7f4c403d15b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1610>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1670>, <src.core.models.report_row.ReportRow object at 0x7f4c403d16d0>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1730>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1790>, <src.core.models.report_row.ReportRow object at 0x7f4c403d17f0>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1850>, <src.core.models.report_row.ReportRow object at 0x7f4c403d18b0>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1910>, <src.core.models.report_row.ReportRow object at 0x7f4c403d1970>]2025-06-12 14:57:01,745 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-06-12 14:57:01,747 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/32B-I-20250613.csv

