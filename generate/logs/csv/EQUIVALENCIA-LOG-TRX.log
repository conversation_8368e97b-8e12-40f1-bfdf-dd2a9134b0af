2025-06-13 06:40:08,048 - root - INFO - No se encontraron datos para exportar.
2025-06-13 06:40:08,050 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/PDP-EQUIVALENCIA-LOG-TRX-********.csv
2025-06-13 06:40:08,061 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-13 06:40:08,204 - root - INFO - Archivo: PDP-EQUIVALENCIA-LOG-TRX-********.csv subido a: s3://prd-datalake-reports-************/FCOMPARTAMOS/2025-06-13/EQUIVALENCIA-LOG-TRX/PDP-EQUIVALENCIA-LOG-TRX-********.csv
SELECT 
"TransactionID",B."TransferID_Mob" , "FinancialTransactionID",A."ExternalTransactionID","DateTime","InitiatingUser","RealUser",A."FromID",
"FromMSISDN","FromUsername","FromProfile","FromAccountID",B."From_AccountID_Mobiquity"  ,"FromAccountType","FromFee","FromLoyaltyReward","FromLoyaltyFee",
A."ToID","ToMSISDN","ToUsername","ToProfile","ToAccountID",B."To_AccountID_Mobiquity" ,"ToAccountType","ToFee","ToLoyaltyReward","ToLoyaltyFee",A."TransactionType",
A."Amount",A."Currency","TransactionStatus",A."Context",A."Comment",B."ReversalID" ,A."From_BankDomain",A."To_BankDomain"
FROM USR_DATALAKE.LOG_TRX_FINAL A
INNER JOIN  USR_DATALAKE.PRE_LOG_TRX B ON A."TransactionID" =B."TransferID" 
WHERE TRUNC(TO_DATE(A."DateTime", 'YYYY-MM-DD HH24:MI:SS')) >= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))

Ejecutando query: SELECT 
"TransactionID",B."TransferID_Mob" , "FinancialTransactionID",A."ExternalTransactionID","DateTime","InitiatingUser","RealUser",A."FromID",
"FromMSISDN","FromUsername","FromProfile","FromAccountID",B."From_AccountID_Mobiquity"  ,"FromAccountType","FromFee","FromLoyaltyReward","FromLoyaltyFee",
A."ToID","ToMSISDN","ToUsername","ToProfile","ToAccountID",B."To_AccountID_Mobiquity" ,"ToAccountType","ToFee","ToLoyaltyReward","ToLoyaltyFee",A."TransactionType",
A."Amount",A."Currency","TransactionStatus",A."Context",A."Comment",B."ReversalID" ,A."From_BankDomain",A."To_BankDomain"
FROM USR_DATALAKE.LOG_TRX_FINAL A
INNER JOIN  USR_DATALAKE.PRE_LOG_TRX B ON A."TransactionID" =B."TransferID" 
WHERE TRUNC(TO_DATE(A."DateTime", 'YYYY-MM-DD HH24:MI:SS')) >= TRUNC(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS'))

No se encontraron registros en la consulta.
[]
