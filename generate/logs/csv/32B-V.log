2025-06-12 14:55:01,614 - root - INFO - <PERSON><PERSON> encontrados. Procediendo a exportar el archivo CSV.
2025-06-12 14:55:01,616 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/32B-V-20250613.csv
WITH
WALLETS AS (
	SELECT
	MW.WALLET_NUMBER,
	MW.USER_ID,
	MW.ISSUER_ID,
	ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MODIFIED_ON ASC) AS ORDEN
	FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW
),
USER_DATA AS (
SELECT
	UP.USER_ID,
	UP.GENDER,
	KC.ID_TYPE,
	ID.ISSUER_CODE,
	UP.STATUS
FROM PDP_PROD10_MAINDB.USER_PROFILE UP
INNER JOIN WALLETS mw ON UP.USER_ID = MW.USER_ID AND MW.ORDEN = 1
LEFT JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON MW.ISSUER_ID = ID.ISSUER_ID
INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KC ON UP.KYC_ID = KC.KYC_ID
WHERE TO_CHAR(UP.CREATED_ON,'YYYYMM') < TO_CHAR(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')-3,'YYYYMM') --CAMBIAR POR LA FECHA CIERRE DE CORTE
),
TITULARES_FIN_MES AS (
SELECT ISSUER_CODE, 
	CASE WHEN GENDER = 'GEN_FEM' THEN 1 ELSE 0 END AS FEMENINO,
	CASE WHEN GENDER = 'GEN_MAL' THEN 1 ELSE 0 END AS MASCULINO,
	CASE 
		WHEN ID_TYPE IN ('DNI','CE','OTHER') THEN 'NATURAL'
		WHEN ID_TYPE = 'RUC' OR ID_TYPE IS NULL THEN 'JURIDICO'
	END AS TIP_DOC
FROM USER_DATA UD
WHERE STATUS = 'Y'
),
DETALLE_REPORTE_32B_V AS (
SELECT
REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0104','') AS DESC_EMISOR,
COALESCE(SUM(CASE WHEN T2.TIP_DOC = 'NATURAL' AND T2.FEMENINO = 1 THEN 1 ELSE 0 END),0) AS NUM_PERSONA_NATURAL_FEMENINO,
COALESCE(SUM(CASE WHEN T2.TIP_DOC = 'NATURAL' AND T2.MASCULINO = 1 THEN 1 ELSE 0 END),0) AS NUM_PERSONA_NATURAL_MASCULINO,
COALESCE(SUM(CASE WHEN T2.TIP_DOC = 'JURIDICO' THEN 1 ELSE 0 END),0) AS NUM_PERSONA_JURIDICA
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID
LEFT JOIN TITULARES_FIN_MES T2
ON ID.ISSUER_CODE = T2.ISSUER_CODE
GROUP BY 
ID.ISSUER_CODE
)
--EXPORTAR TXT 32B_V
SELECT
DESC_EMISOR AS EMISOR,
LPAD('100',6,'0') || LPAD(CAST((NUM_PERSONA_NATURAL_FEMENINO+NUM_PERSONA_NATURAL_MASCULINO) AS VARCHAR(80)),9,'0') || LPAD('0',36,'0') AS TRAMA
FROM DETALLE_REPORTE_32B_V
UNION
SELECT
DESC_EMISOR AS EMISOR,
LPAD('150',6,'0') || LPAD(CAST(NUM_PERSONA_NATURAL_FEMENINO AS VARCHAR(80)),9,'0') || LPAD('0',36,'0') AS TRAMA
FROM DETALLE_REPORTE_32B_V
UNION
SELECT
DESC_EMISOR AS EMISOR,
LPAD('175',6,'0') || LPAD(CAST(NUM_PERSONA_NATURAL_MASCULINO AS VARCHAR(80)),9,'0') || LPAD('0',36,'0') AS TRAMA
FROM DETALLE_REPORTE_32B_V
UNION
SELECT
DESC_EMISOR AS EMISOR,
LPAD('200',6,'0') || LPAD(CAST(NUM_PERSONA_JURIDICA AS VARCHAR(80)),9,'0') || LPAD('0',36,'0') AS TRAMA
FROM DETALLE_REPORTE_32B_V
ORDER BY 1,2 ASC


--EJECUTAR Y GUARDAR CON EL NOMBRE: 32B-V-YYYYMMDD.csv (20250314)

Ejecutando query: WITH
WALLETS AS (
	SELECT
	MW.WALLET_NUMBER,
	MW.USER_ID,
	MW.ISSUER_ID,
	ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MODIFIED_ON ASC) AS ORDEN
	FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW
),
USER_DATA AS (
SELECT
	UP.USER_ID,
	UP.GENDER,
	KC.ID_TYPE,
	ID.ISSUER_CODE,
	UP.STATUS
FROM PDP_PROD10_MAINDB.USER_PROFILE UP
INNER JOIN WALLETS mw ON UP.USER_ID = MW.USER_ID AND MW.ORDEN = 1
LEFT JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON MW.ISSUER_ID = ID.ISSUER_ID
INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KC ON UP.KYC_ID = KC.KYC_ID
WHERE TO_CHAR(UP.CREATED_ON,'YYYYMM') < TO_CHAR(TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')-3,'YYYYMM') --CAMBIAR POR LA FECHA CIERRE DE CORTE
),
TITULARES_FIN_MES AS (
SELECT ISSUER_CODE, 
	CASE WHEN GENDER = 'GEN_FEM' THEN 1 ELSE 0 END AS FEMENINO,
	CASE WHEN GENDER = 'GEN_MAL' THEN 1 ELSE 0 END AS MASCULINO,
	CASE 
		WHEN ID_TYPE IN ('DNI','CE','OTHER') THEN 'NATURAL'
		WHEN ID_TYPE = 'RUC' OR ID_TYPE IS NULL THEN 'JURIDICO'
	END AS TIP_DOC
FROM USER_DATA UD
WHERE STATUS = 'Y'
),
DETALLE_REPORTE_32B_V AS (
SELECT
REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0104','') AS DESC_EMISOR,
COALESCE(SUM(CASE WHEN T2.TIP_DOC = 'NATURAL' AND T2.FEMENINO = 1 THEN 1 ELSE 0 END),0) AS NUM_PERSONA_NATURAL_FEMENINO,
COALESCE(SUM(CASE WHEN T2.TIP_DOC = 'NATURAL' AND T2.MASCULINO = 1 THEN 1 ELSE 0 END),0) AS NUM_PERSONA_NATURAL_MASCULINO,
COALESCE(SUM(CASE WHEN T2.TIP_DOC = 'JURIDICO' THEN 1 ELSE 0 END),0) AS NUM_PERSONA_JURIDICA
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID
LEFT JOIN TITULARES_FIN_MES T2
ON ID.ISSUER_CODE = T2.ISSUER_CODE
GROUP BY 
ID.ISSUER_CODE
)
--EXPORTAR TXT 32B_V
SELECT
DESC_EMISOR AS EMISOR,
LPAD('100',6,'0') || LPAD(CAST((NUM_PERSONA_NATURAL_FEMENINO+NUM_PERSONA_NATURAL_MASCULINO) AS VARCHAR(80)),9,'0') || LPAD('0',36,'0') AS TRAMA
FROM DETALLE_REPORTE_32B_V
UNION
SELECT
DESC_EMISOR AS EMISOR,
LPAD('150',6,'0') || LPAD(CAST(NUM_PERSONA_NATURAL_FEMENINO AS VARCHAR(80)),9,'0') || LPAD('0',36,'0') AS TRAMA
FROM DETALLE_REPORTE_32B_V
UNION
SELECT
DESC_EMISOR AS EMISOR,
LPAD('175',6,'0') || LPAD(CAST(NUM_PERSONA_NATURAL_MASCULINO AS VARCHAR(80)),9,'0') || LPAD('0',36,'0') AS TRAMA
FROM DETALLE_REPORTE_32B_V
UNION
SELECT
DESC_EMISOR AS EMISOR,
LPAD('200',6,'0') || LPAD(CAST(NUM_PERSONA_JURIDICA AS VARCHAR(80)),9,'0') || LPAD('0',36,'0') AS TRAMA
FROM DETALLE_REPORTE_32B_V
ORDER BY 1,2 ASC


--EJECUTAR Y GUARDAR CON EL NOMBRE: 32B-V-YYYYMMDD.csv (20250314)

[<src.core.models.report_row.ReportRow object at 0x7f1fc821f6d0>, <src.core.models.report_row.ReportRow object at 0x7f1fc821f5b0>, <src.core.models.report_row.ReportRow object at 0x7f1fc821f7f0>, <src.core.models.report_row.ReportRow object at 0x7f1fc821f850>, <src.core.models.report_row.ReportRow object at 0x7f1fc821f8b0>, <src.core.models.report_row.ReportRow object at 0x7f1fc821f910>, <src.core.models.report_row.ReportRow object at 0x7f1fc821f970>, <src.core.models.report_row.ReportRow object at 0x7f1fc821f9d0>, <src.core.models.report_row.ReportRow object at 0x7f1fc821fa30>, <src.core.models.report_row.ReportRow object at 0x7f1fc821fa90>, <src.core.models.report_row.ReportRow object at 0x7f1fc821faf0>, <src.core.models.report_row.ReportRow object at 0x7f1fc821fb50>, <src.core.models.report_row.ReportRow object at 0x7f1fc821fbb0>, <src.core.models.report_row.ReportRow object at 0x7f1fc821fc10>, <src.core.models.report_row.ReportRow object at 0x7f1fc821fc70>, <src.core.models.report_row.ReportRow object at 0x7f1fc821fcd0>, <src.core.models.report_row.ReportRow object at 0x7f1fc821fd30>, <src.core.models.report_row.ReportRow object at 0x7f1fc821fd90>, <src.core.models.report_row.ReportRow object at 0x7f1fc821fdf0>, <src.core.models.report_row.ReportRow object at 0x7f1fc821fe50>, <src.core.models.report_row.ReportRow object at 0x7f1fc821feb0>, <src.core.models.report_row.ReportRow object at 0x7f1fc821ff10>, <src.core.models.report_row.ReportRow object at 0x7f1fc821ff70>, <src.core.models.report_row.ReportRow object at 0x7f1fc821ffd0>]
