WITH
WALLETS_PREV AS (
SELECT 
	MW.USER_ID,
	MW.WALLET_NUMBER,
	MW.ISSUER_ID,
	UPPER(CG.GRADE_NAME) AS GRADE_NAME,
	ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) AS ORDEN
FROM PDP_PROD10_MAINDBBUS.MTX_WALLET mw
INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
WHERE trunc(MW.CREATED_ON) < (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES)
),
WALLETS_POST AS (
SELECT 
	MW.USER_ID,
	MW.WALLET_NUMBER,
	MW.ISSUER_ID,
	UPPER(CG.GRADE_NAME) AS GRADE_NAME,
	ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) AS ORDEN
FROM PDP_PROD10_MAINDBBUS.MTX_WALLET mw
INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
WHERE trunc(MW.CREATED_ON) < TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')
),
WALLETS_ORIG_PREV AS (
	SELECT 
		UP.ATTR7 AS USER_ID, 
		MW.WALLET_NUMBER, 
		MW.ISSUER_ID, 
		UP.CREATED_ON,
		CASE 
			WHEN MW.GRADE_NAME IN ('NORMAL ACCOUNT PROFILE','MERCHANT ACCOUNT PROFILE') THEN 'SIMPLIFICADA'
			WHEN (MW.GRADE_NAME IN ('NORMAL GENERAL ACCOUNT PROFILE','MERCHANT GENERAL ACCOUNT PROFILE') OR UP.MSISDN IN ('***********','***********','***********','***********'))THEN 'GENERAL'
			WHEN MW.GRADE_NAME IN ('OPERATIONAL ACCOUNT PROFILE','SERVICE PROVIDER HIGH CONTENTION ACCOUNT','VIRTUAL OPERATIONAL ACCOUNT PROFILE') THEN 'OPERACIONAL'
			ELSE ''
		END AS TIPO_CUENTA, 
		KD.ID_TYPE AS TIP_DOC
	FROM PDP_PROD10_MAINDB.USER_PROFILE UP
	INNER JOIN WALLETS_PREV MW ON UP.USER_ID = MW.USER_ID AND MW.ORDEN=1
	INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KD ON UP.KYC_ID = KD.KYC_ID
),
WALLETS_ORIG_POST AS (
	SELECT 
		UP.ATTR7 AS USER_ID, 
		MW.WALLET_NUMBER, 
		MW.ISSUER_ID, 
		UP.CREATED_ON,
		CASE 
			WHEN MW.GRADE_NAME IN ('NORMAL ACCOUNT PROFILE','MERCHANT ACCOUNT PROFILE') THEN 'SIMPLIFICADA'
			WHEN (MW.GRADE_NAME IN ('NORMAL GENERAL ACCOUNT PROFILE','MERCHANT GENERAL ACCOUNT PROFILE') OR UP.MSISDN IN ('***********','***********','***********','***********'))THEN 'GENERAL'
			WHEN MW.GRADE_NAME IN ('OPERATIONAL ACCOUNT PROFILE','SERVICE PROVIDER HIGH CONTENTION ACCOUNT','VIRTUAL OPERATIONAL ACCOUNT PROFILE') THEN 'OPERACIONAL'
			ELSE ''
		END AS TIPO_CUENTA, 
		KD.ID_TYPE AS TIP_DOC
	FROM PDP_PROD10_MAINDB.USER_PROFILE UP
	INNER JOIN WALLETS_POST MW ON UP.USER_ID = MW.USER_ID AND MW.ORDEN=1
	INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KD ON UP.KYC_ID = KD.KYC_ID
),
BALANCE_INICIO_MES AS (
SELECT 
	W.USER_ID,
	W.ISSUER_ID AS EMISOR,
	W.TIPO_CUENTA,
	W.TIP_DOC,
	BP.MONTO
FROM WALLETS_ORIG_PREV W
INNER JOIN USR_DATALAKE.USER_BALANCES BP ON W.WALLET_NUMBER = BP.WALLET_NUMBER AND BP.FECHA_ORIGEN = (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES)
WHERE W.TIPO_CUENTA IS NOT NULL
),
BALANCE_FIN_MES AS (
SELECT
	W.USER_ID,
	W.ISSUER_ID AS EMISOR,
	W.TIPO_CUENTA,
	W.TIP_DOC,
	BP.MONTO
FROM WALLETS_ORIG_POST W
INNER JOIN USR_DATALAKE.USER_BALANCES BP ON W.WALLET_NUMBER = BP.WALLET_NUMBER AND BP.FECHA_ORIGEN = TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')
WHERE TIPO_CUENTA IS NOT NULL
),
EMISOR_CODIGO AS (
SELECT 
	REPLACE(REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'',''),'0104','') AS DESC_EMISOR,
	ID.ISSUER_ID AS EMISOR,
	C.CODIGO
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID
CROSS JOIN (SELECT '100' AS CODIGO FROM dual UNION ALL 
            SELECT '200' AS CODIGO FROM dual UNION ALL
            SELECT '210' AS CODIGO FROM dual UNION ALL
            SELECT '220' AS CODIGO FROM dual UNION ALL
            SELECT '230' AS CODIGO FROM dual UNION ALL
            SELECT '300' AS CODIGO FROM dual UNION ALL
            SELECT '400' AS CODIGO FROM dual UNION ALL
            SELECT '410' AS CODIGO FROM dual UNION ALL
            SELECT '420' AS CODIGO FROM dual UNION ALL
            SELECT '430' AS CODIGO FROM dual) C
),
INIT_CODE AS (
SELECT EMISOR, '100' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_INICIO_MES WHERE TIPO_CUENTA = 'SIMPLIFICADA' AND TIP_DOC IN ('DNI','CE') UNION ALL
SELECT EMISOR, '200' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_INICIO_MES WHERE ((TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN ('DNI','CE','OTHER','RUC')) OR TIPO_CUENTA = 'OPERACIONAL') UNION ALL
SELECT EMISOR, '210' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_INICIO_MES WHERE TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN ('DNI','CE','OTHER') UNION ALL
SELECT EMISOR, '220' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_INICIO_MES WHERE TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN 'RUC' UNION ALL
SELECT EMISOR, '230' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_INICIO_MES WHERE TIPO_CUENTA = 'OPERACIONAL'
),
END_CODE AS (
SELECT EMISOR, '300' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_FIN_MES WHERE TIPO_CUENTA = 'SIMPLIFICADA' AND TIP_DOC IN ('DNI','CE') UNION ALL
SELECT EMISOR, '400' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_FIN_MES WHERE ((TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN ('DNI','CE','OTHER','RUC')) OR TIPO_CUENTA = 'OPERACIONAL') UNION ALL
SELECT EMISOR, '410' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_FIN_MES WHERE TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN ('DNI','CE','OTHER') UNION ALL
SELECT EMISOR, '420' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_FIN_MES WHERE TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN ('RUC') UNION ALL
SELECT EMISOR, '430' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_FIN_MES WHERE TIPO_CUENTA = 'OPERACIONAL'
),
UNIT AS (
SELECT 
	 EC.DESC_EMISOR,
	 EC.CODIGO,
	 COALESCE(SUM(IC.MONTO),0) AS MONTO_SOLES,
	 COALESCE(SUM(IC.MONTO_DOLAR),0) AS MONTO_DOLAR
FROM EMISOR_CODIGO EC 
LEFT JOIN INIT_CODE IC ON EC.CODIGO = IC.FILA AND EC.EMISOR = IC.EMISOR
WHERE EC.CODIGO < '250'
GROUP BY 
	EC.DESC_EMISOR,
	EC.CODIGO
UNION ALL
SELECT 
	 EC.DESC_EMISOR,
	 EC.CODIGO,
	 COALESCE(SUM(ENC.MONTO),0) AS MONTO_SOLES,
	 COALESCE(SUM(ENC.MONTO_DOLAR),0) AS MONTO_DOLAR
FROM EMISOR_CODIGO EC 
LEFT JOIN END_CODE ENC ON EC.CODIGO = ENC.FILA AND EC.EMISOR = ENC.EMISOR
WHERE EC.CODIGO > '250'
GROUP BY 
	EC.DESC_EMISOR,
	EC.CODIGO
)
SELECT 
	U.DESC_EMISOR AS EMISOR,
	LPAD(U.CODIGO,6,'0') || LPAD(U.MONTO_SOLES,18,'0') || LPAD(U.MONTO_DOLAR,18,'0') || LPAD(U.MONTO_SOLES+U.MONTO_DOLAR,18,'0') AS TRAMA
FROM UNIT U
ORDER BY 1,2 ASC

--EJECUTAR Y GUARDAR CON EL NOMBRE: 32B-II-YYYYMMDD.csv (20250314)

Ejecutando query: WITH
WALLETS_PREV AS (
SELECT 
	MW.USER_ID,
	MW.WALLET_NUMBER,
	MW.ISSUER_ID,
	UPPER(CG.GRADE_NAME) AS GRADE_NAME,
	ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) AS ORDEN
FROM PDP_PROD10_MAINDBBUS.MTX_WALLET mw
INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
WHERE trunc(MW.CREATED_ON) < (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES)
),
WALLETS_POST AS (
SELECT 
	MW.USER_ID,
	MW.WALLET_NUMBER,
	MW.ISSUER_ID,
	UPPER(CG.GRADE_NAME) AS GRADE_NAME,
	ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) AS ORDEN
FROM PDP_PROD10_MAINDBBUS.MTX_WALLET mw
INNER JOIN PDP_PROD10_MAINDB.CHANNEL_GRADES CG ON MW.USER_GRADE = CG.GRADE_CODE
WHERE trunc(MW.CREATED_ON) < TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')
),
WALLETS_ORIG_PREV AS (
	SELECT 
		UP.ATTR7 AS USER_ID, 
		MW.WALLET_NUMBER, 
		MW.ISSUER_ID, 
		UP.CREATED_ON,
		CASE 
			WHEN MW.GRADE_NAME IN ('NORMAL ACCOUNT PROFILE','MERCHANT ACCOUNT PROFILE') THEN 'SIMPLIFICADA'
			WHEN (MW.GRADE_NAME IN ('NORMAL GENERAL ACCOUNT PROFILE','MERCHANT GENERAL ACCOUNT PROFILE') OR UP.MSISDN IN ('***********','***********','***********','***********'))THEN 'GENERAL'
			WHEN MW.GRADE_NAME IN ('OPERATIONAL ACCOUNT PROFILE','SERVICE PROVIDER HIGH CONTENTION ACCOUNT','VIRTUAL OPERATIONAL ACCOUNT PROFILE') THEN 'OPERACIONAL'
			ELSE ''
		END AS TIPO_CUENTA, 
		KD.ID_TYPE AS TIP_DOC
	FROM PDP_PROD10_MAINDB.USER_PROFILE UP
	INNER JOIN WALLETS_PREV MW ON UP.USER_ID = MW.USER_ID AND MW.ORDEN=1
	INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KD ON UP.KYC_ID = KD.KYC_ID
),
WALLETS_ORIG_POST AS (
	SELECT 
		UP.ATTR7 AS USER_ID, 
		MW.WALLET_NUMBER, 
		MW.ISSUER_ID, 
		UP.CREATED_ON,
		CASE 
			WHEN MW.GRADE_NAME IN ('NORMAL ACCOUNT PROFILE','MERCHANT ACCOUNT PROFILE') THEN 'SIMPLIFICADA'
			WHEN (MW.GRADE_NAME IN ('NORMAL GENERAL ACCOUNT PROFILE','MERCHANT GENERAL ACCOUNT PROFILE') OR UP.MSISDN IN ('***********','***********','***********','***********'))THEN 'GENERAL'
			WHEN MW.GRADE_NAME IN ('OPERATIONAL ACCOUNT PROFILE','SERVICE PROVIDER HIGH CONTENTION ACCOUNT','VIRTUAL OPERATIONAL ACCOUNT PROFILE') THEN 'OPERACIONAL'
			ELSE ''
		END AS TIPO_CUENTA, 
		KD.ID_TYPE AS TIP_DOC
	FROM PDP_PROD10_MAINDB.USER_PROFILE UP
	INNER JOIN WALLETS_POST MW ON UP.USER_ID = MW.USER_ID AND MW.ORDEN=1
	INNER JOIN PDP_PROD10_MAINDB.KYC_DETAILS KD ON UP.KYC_ID = KD.KYC_ID
),
BALANCE_INICIO_MES AS (
SELECT 
	W.USER_ID,
	W.ISSUER_ID AS EMISOR,
	W.TIPO_CUENTA,
	W.TIP_DOC,
	BP.MONTO
FROM WALLETS_ORIG_PREV W
INNER JOIN USR_DATALAKE.USER_BALANCES BP ON W.WALLET_NUMBER = BP.WALLET_NUMBER AND BP.FECHA_ORIGEN = (SELECT MIN(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES)
WHERE W.TIPO_CUENTA IS NOT NULL
),
BALANCE_FIN_MES AS (
SELECT
	W.USER_ID,
	W.ISSUER_ID AS EMISOR,
	W.TIPO_CUENTA,
	W.TIP_DOC,
	BP.MONTO
FROM WALLETS_ORIG_POST W
INNER JOIN USR_DATALAKE.USER_BALANCES BP ON W.WALLET_NUMBER = BP.WALLET_NUMBER AND BP.FECHA_ORIGEN = TO_DATE('2025-06-12 00:00:00','YYYY-MM-DD HH24:MI:SS')
WHERE TIPO_CUENTA IS NOT NULL
),
EMISOR_CODIGO AS (
SELECT 
	REPLACE(REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'',''),'0104','') AS DESC_EMISOR,
	ID.ISSUER_ID AS EMISOR,
	C.CODIGO
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID
CROSS JOIN (SELECT '100' AS CODIGO FROM dual UNION ALL 
            SELECT '200' AS CODIGO FROM dual UNION ALL
            SELECT '210' AS CODIGO FROM dual UNION ALL
            SELECT '220' AS CODIGO FROM dual UNION ALL
            SELECT '230' AS CODIGO FROM dual UNION ALL
            SELECT '300' AS CODIGO FROM dual UNION ALL
            SELECT '400' AS CODIGO FROM dual UNION ALL
            SELECT '410' AS CODIGO FROM dual UNION ALL
            SELECT '420' AS CODIGO FROM dual UNION ALL
            SELECT '430' AS CODIGO FROM dual) C
),
INIT_CODE AS (
SELECT EMISOR, '100' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_INICIO_MES WHERE TIPO_CUENTA = 'SIMPLIFICADA' AND TIP_DOC IN ('DNI','CE') UNION ALL
SELECT EMISOR, '200' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_INICIO_MES WHERE ((TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN ('DNI','CE','OTHER','RUC')) OR TIPO_CUENTA = 'OPERACIONAL') UNION ALL
SELECT EMISOR, '210' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_INICIO_MES WHERE TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN ('DNI','CE','OTHER') UNION ALL
SELECT EMISOR, '220' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_INICIO_MES WHERE TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN 'RUC' UNION ALL
SELECT EMISOR, '230' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_INICIO_MES WHERE TIPO_CUENTA = 'OPERACIONAL'
),
END_CODE AS (
SELECT EMISOR, '300' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_FIN_MES WHERE TIPO_CUENTA = 'SIMPLIFICADA' AND TIP_DOC IN ('DNI','CE') UNION ALL
SELECT EMISOR, '400' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_FIN_MES WHERE ((TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN ('DNI','CE','OTHER','RUC')) OR TIPO_CUENTA = 'OPERACIONAL') UNION ALL
SELECT EMISOR, '410' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_FIN_MES WHERE TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN ('DNI','CE','OTHER') UNION ALL
SELECT EMISOR, '420' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_FIN_MES WHERE TIPO_CUENTA = 'GENERAL' AND TIP_DOC IN ('RUC') UNION ALL
SELECT EMISOR, '430' AS FILA, MONTO, 0 AS MONTO_DOLAR FROM BALANCE_FIN_MES WHERE TIPO_CUENTA = 'OPERACIONAL'
),
UNIT AS (
SELECT 
	 EC.DESC_EMISOR,
	 EC.CODIGO,
	 COALESCE(SUM(IC.MONTO),0) AS MONTO_SOLES,
	 COALESCE(SUM(IC.MONTO_DOLAR),0) AS MONTO_DOLAR
FROM EMISOR_CODIGO EC 
LEFT JOIN INIT_CODE IC ON EC.CODIGO = IC.FILA AND EC.EMISOR = IC.EMISOR
WHERE EC.CODIGO < '250'
GROUP BY 
	EC.DESC_EMISOR,
	EC.CODIGO
UNION ALL
SELECT 
	 EC.DESC_EMISOR,
	 EC.CODIGO,
	 COALESCE(SUM(ENC.MONTO),0) AS MONTO_SOLES,
	 COALESCE(SUM(ENC.MONTO_DOLAR),0) AS MONTO_DOLAR
FROM EMISOR_CODIGO EC 
LEFT JOIN END_CODE ENC ON EC.CODIGO = ENC.FILA AND EC.EMISOR = ENC.EMISOR
WHERE EC.CODIGO > '250'
GROUP BY 
	EC.DESC_EMISOR,
	EC.CODIGO
)
SELECT 
	U.DESC_EMISOR AS EMISOR,
	LPAD(U.CODIGO,6,'0') || LPAD(U.MONTO_SOLES,18,'0') || LPAD(U.MONTO_DOLAR,18,'0') || LPAD(U.MONTO_SOLES+U.MONTO_DOLAR,18,'0') AS TRAMA
FROM UNIT U
ORDER BY 1,2 ASC

--EJECUTAR Y GUARDAR CON EL NOMBRE: 32B-II-YYYYMMDD.csv (20250314)

2025-06-12 14:55:49,958 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-06-12 14:55:49,960 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/32B-II-20250613.csv
[<src.core.models.report_row.ReportRow object at 0x7f7b1d3576d0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d3575b0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d3577f0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357850>, <src.core.models.report_row.ReportRow object at 0x7f7b1d3578b0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357910>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357970>, <src.core.models.report_row.ReportRow object at 0x7f7b1d3579d0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357a30>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357a90>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357af0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357b50>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357bb0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357c10>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357c70>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357cd0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357d30>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357d90>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357df0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357e50>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357eb0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357f10>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357f70>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357fd0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357550>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357610>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357700>, <src.core.models.report_row.ReportRow object at 0x7f7b1d3b0a90>, <src.core.models.report_row.ReportRow object at 0x7f7b1d357790>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d250>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d2b0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d310>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d370>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d3d0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d430>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d490>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d4f0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d550>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d5b0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d610>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d670>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d6d0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d730>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d790>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d7f0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d850>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d8b0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d910>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d970>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37d9d0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37da30>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37da90>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37daf0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37db50>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37dbb0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37dc10>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37dc70>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37dcd0>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37dd30>, <src.core.models.report_row.ReportRow object at 0x7f7b1d37dd90>]
