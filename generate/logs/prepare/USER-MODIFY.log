INFO:__main__:Starting data extraction from MySQL
/home/<USER>/generate/prepare/mysql_extract.py:23: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, connection, params=[fecha])
INFO:__main__:Loading data to Oracle, File: /home/<USER>/output/csv/user_account_history_20250613.csv
INFO:__main__:USER-MODIFY: OK
INFO:__main__:Process completed successfull
   USER_ID  ACCOUNT_ID  ... ISSUER_OLD        GRADE_OLD
0  2436207     3331689  ...        504  Normal Account 
1   690283     1625009  ...        504  Normal Account 
2  3333912     4202650  ...        504  Normal Account 
3  2428062     3323559  ...        504  Normal Account 
4  3108423     3991157  ...        504  Normal Account 
5  1789013     2696305  ...        511  Normal Account 

[6 rows x 8 columns]
6 registros insertados exitosamente.
