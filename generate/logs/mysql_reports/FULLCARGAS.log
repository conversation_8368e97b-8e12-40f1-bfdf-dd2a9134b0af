Fecha convertida a formato correcto: 2025-06-12
Fecha archivo convertida a formato correcto: 20250613
Leyendo tablas -> Cabecera: None
Leyendo tablas -> <PERSON>uerpo: SELECT id, fin_trx_id, ext_trx_id, datetime, trx_type,
from_msisdn, CONCAT(to_username, '@fullcarga'), amount FROM conciliacion_fullcarga
where DATE_FORMAT(datetime,'%Y-%m-%d')= '{fecha}';
Leyendo tablas -> Pie: SELECT 'TOTAL FULLCARGA', ROUND(COALESCE(SUM(amount),0),2) FROM conciliacion_fullcarga
where DATE_FORMAT(datetime,'%Y-%m-%d')= '{fecha}';
Connection opened successfully.
Error mysql: object of type 'NoneType' has no len()
Database connection closed.
Respuesta de la BD, count 0
Connection opened successfully.
Database connection closed.
Respuesta de la BD, count 0
Connection opened successfully.
Database connection closed.
Respuesta de la BD, count 1
cuerpo None
pie [{'TOTAL FULLCARGA': 'TOTAL FULLCARGA', 'ROUND(COALESCE(SUM(amount),0),2)': 0.0}]
BD no devolvió nada
Creando archivo.
Escribiendo archivo...
Datos del archivo /home/<USER>/output/mysql/PDP-REPORTE-FULLCARGA_20250613.csv
Archivo escrito. /home/<USER>/output/mysql/PDP-REPORTE-FULLCARGA_20250613.csv
Archivo cerrado. /home/<USER>/output/mysql/PDP-REPORTE-FULLCARGA_20250613.csv
Se creo archivo.
PDP-REPORTE-FULLCARGA_20250613.csv
