Fecha convertida a formato correcto: 2025-06-12
Fecha archivo convertida a formato correcto: 20250613
Leyendo tablas -> Cabecera: None
Leyendo tablas -> Cuerpo: SELECT financial_id, payment_time, numero_operacion, msisdn, nro_suministro , registro, CONCAT(REPLACE(empresa, ' ', '_'),'@WU'), service_charge, total FROM Pago_servicios_wu WHERE DATE(payment_time) = '{fecha}' AND emisor = 'WU'
Leyendo tablas -> Pie: SELECT 'TOTAL WU', SUM(total) FROM Pago_servicios_wu WHERE DATE(payment_time) = '{fecha}' AND emisor = 'WU'
Connection opened successfully.
Error mysql: object of type 'NoneType' has no len()
Database connection closed.
Respuesta de la BD, count 0
Connection opened successfully.
Database connection closed.
Respuesta de la BD, count 378
Connection opened successfully.
Database connection closed.
Respuesta de la BD, count 1
cuerpo [{'financial_id': Decimal('5902506120702357959487'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 2, 35), 'numero_operacion': '5695993278569', 'msisdn': '51929769310', 'nro_suministro': '64784386', 'registro': 'PEA716C12020182025-06-1207.02.36989616060000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('23.80')}, {'financial_id': Decimal('5902506120709133389699'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 9, 12), 'numero_operacion': '0', 'msisdn': '51961209921', 'nro_suministro': '0616220', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('423.00')}, {'financial_id': Decimal('1162506120709482659545'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 9, 47), 'numero_operacion': '0', 'msisdn': '51961209921', 'nro_suministro': '0616220', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('423.00')}, {'financial_id': Decimal('8502506120710233319041'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 10, 22), 'numero_operacion': '0', 'msisdn': '51961209921', 'nro_suministro': '0616220', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('423.00')}, {'financial_id': Decimal('5902506120713511949841'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 13, 50), 'numero_operacion': '5695993278842', 'msisdn': '51981265429', 'nro_suministro': '981265429', 'registro': 'PEA716C12130942025-06-1207.13.51990016100000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('51.40')}, {'financial_id': Decimal('5902506120722434290166'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 22, 42), 'numero_operacion': '5695993279023', 'msisdn': '51983454425', 'nro_suministro': '48597820', 'registro': 'PEA716C12222832025-06-1207.22.44990116120000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('24.80')}, {'financial_id': Decimal('8502506120722482549455'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 22, 46), 'numero_operacion': '5695993279022', 'msisdn': '51907125548', 'nro_suministro': '48551465', 'registro': 'PEA716C12221712025-06-1207.22.48990216130000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('109.70')}, {'financial_id': Decimal('5902506120732135390486'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 32, 13), 'numero_operacion': '5695993279319', 'msisdn': '51954805973', 'nro_suministro': '985009952', 'registro': 'PEA716C12314702025-06-1207.32.14990316140000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('5902506120732466760515'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 32, 46), 'numero_operacion': '5695993279325', 'msisdn': '51985846713', 'nro_suministro': '1207808', 'registro': 'PEA716C12320352025-06-1207.32.47990416160000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('44.70')}, {'financial_id': Decimal('5902506120733549320553'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 33, 54), 'numero_operacion': '5695993279333', 'msisdn': '51930661878', 'nro_suministro': '6374971 ', 'registro': 'PEA716C12330392025-06-1207.33.55990516170000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('76.30')}, {'financial_id': Decimal('5902506120734245600567'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 34, 24), 'numero_operacion': '5695993279381', 'msisdn': '51985846713', 'nro_suministro': '2906586', 'registro': 'PEA716C12335682025-06-1207.34.25990616190000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('77.10')}, {'financial_id': Decimal('1162506120738347690573'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 38, 34), 'numero_operacion': '5695993279456', 'msisdn': '51930661878', 'nro_suministro': '6374971 ', 'registro': 'PEA716C12365072025-06-1207.38.35990716210000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('31.90')}, {'financial_id': Decimal('8502506120743025810207'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 43, 2), 'numero_operacion': '5695993279605', 'msisdn': '51933632626', 'nro_suministro': '2680139', 'registro': 'PEA716C12422912025-06-1207.43.03990816230000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('150.00')}, {'financial_id': Decimal('8502506120749242870472'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 49, 23), 'numero_operacion': '5695993279827', 'msisdn': '51928860487', 'nro_suministro': '928860487', 'registro': 'PEA716C12485202025-06-1207.49.25990916250000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('29.16')}, {'financial_id': Decimal('1162506120756264231333'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 56, 25), 'numero_operacion': '5695993280042', 'msisdn': '51984513056', 'nro_suministro': '984513056 ', 'registro': 'PEA716C12545812025-06-1207.56.27991016270000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.90')}, {'financial_id': Decimal('1162506120756286261335'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 56, 28), 'numero_operacion': '5695993280045', 'msisdn': '51921183899', 'nro_suministro': '918771520', 'registro': 'PEA716C12550292025-06-1207.56.29991116290000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('55.85')}, {'financial_id': Decimal('1162506120756360691341'), 'payment_time': datetime.datetime(2025, 6, 12, 7, 56, 35), 'numero_operacion': '5695993280050', 'msisdn': '51930940591', 'nro_suministro': '041548363', 'registro': 'PEA716C12553282025-06-1207.56.36991216310000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('686.10')}, {'financial_id': Decimal('8502506120801258641027'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 1, 25), 'numero_operacion': '5695993280254', 'msisdn': '51943375420', 'nro_suministro': '1788708', 'registro': 'PEA716C13004662025-06-1208.01.26991316320000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('242.50')}, {'financial_id': Decimal('1162506120804095401676'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 4, 9), 'numero_operacion': '5695993280383', 'msisdn': '51943375420', 'nro_suministro': '1006074', 'registro': 'PEA716C13034182025-06-1208.04.10991416330000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('74.50')}, {'financial_id': Decimal('5902506120806085521938'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 6, 8), 'numero_operacion': '5695993280496', 'msisdn': '51920843383', 'nro_suministro': '922402343', 'registro': 'PEA716C13053702025-06-1208.06.09991516340000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.15')}, {'financial_id': Decimal('8502506120807427931308'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 7, 42), 'numero_operacion': '5695993280576', 'msisdn': '51943375420', 'nro_suministro': '4137276', 'registro': 'PEA716C13071792025-06-1208.07.43991616360000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('74.80')}, {'financial_id': Decimal('5902506120807556932017'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 7, 55), 'numero_operacion': '5695993280578', 'msisdn': '51923618269', 'nro_suministro': '923618269', 'registro': 'PEA716C13074142025-06-1208.07.56991716380000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('24.30')}, {'financial_id': Decimal('1162506120808320641872'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 8, 31), 'numero_operacion': '5695993280582', 'msisdn': '51913607158', 'nro_suministro': '06659901', 'registro': 'PEA716C13074602025-06-1208.08.32991816400000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('17.80')}, {'financial_id': Decimal('8502506120810025051418'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 10, 2), 'numero_operacion': '5695993280595', 'msisdn': '51937044631', 'nro_suministro': '979262206 ', 'registro': 'PEA716C13093492025-06-1208.10.03991916410000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506120810520591975'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 10, 51), 'numero_operacion': '5695993280680', 'msisdn': '51979801428', 'nro_suministro': '979801428', 'registro': 'PEA716C13101582025-06-1208.10.52992016430000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.50')}, {'financial_id': Decimal('5902506120813352082283'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 13, 34), 'numero_operacion': '5695993280801', 'msisdn': '51919690672', 'nro_suministro': '919690672', 'registro': 'PEA716C13131062025-06-1208.13.35992116450000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('105.22')}, {'financial_id': Decimal('5902506120817145382497'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 17, 14), 'numero_operacion': '5695993280918', 'msisdn': '51913380078', 'nro_suministro': '78753891', 'registro': 'PEA716C13164512025-06-1208.17.15992216470000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('27.70')}, {'financial_id': Decimal('5902506120818313402564'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 18, 30), 'numero_operacion': '5695993280927', 'msisdn': '51939914141', 'nro_suministro': '16858810', 'registro': 'PEA716C13180592025-06-1208.18.32992316480000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('44.90')}, {'financial_id': Decimal('1162506120824235322662'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 24, 22), 'numero_operacion': '5695993281094', 'msisdn': '51944345365', 'nro_suministro': '15594978', 'registro': 'PEA716C13233202025-06-1208.24.24992416490000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.40')}, {'financial_id': Decimal('5902506120826250042982'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 26, 24), 'numero_operacion': '5695993281228', 'msisdn': '51935674230', 'nro_suministro': '935674230', 'registro': 'PEA716C13255502025-06-1208.26.25992516500000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('47.50')}, {'financial_id': Decimal('1162506120827520572864'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 27, 51), 'numero_operacion': '5695993281356', 'msisdn': '51979450300', 'nro_suministro': '979450300', 'registro': 'PEA716C13263992025-06-1208.27.52992616520000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('37.10')}, {'financial_id': Decimal('1162506120827594232874'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 27, 59), 'numero_operacion': '5695993281363', 'msisdn': '51931538578', 'nro_suministro': '83444986', 'registro': 'PEA716C13274232025-06-1208.28.00992716540000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('8.90')}, {'financial_id': Decimal('1162506120829293622968'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 29, 28), 'numero_operacion': '5695993281373', 'msisdn': '51931538578', 'nro_suministro': '83444986', 'registro': 'PEA716C13290292025-06-1208.29.30992816550000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('8.30')}, {'financial_id': Decimal('1162506120830203633022'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 30, 19), 'numero_operacion': '5695993281478', 'msisdn': '51943513859', 'nro_suministro': '943513859', 'registro': 'PEA716C13294172025-06-1208.30.21992916560000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('36.76')}, {'financial_id': Decimal('5902506120832497733365'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 32, 49), 'numero_operacion': '5695993281647', 'msisdn': '51931538578', 'nro_suministro': '82751884', 'registro': 'PEA716C13323612025-06-1208.32.50993016580000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('5.90')}, {'financial_id': Decimal('5902506120833549303431'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 33, 54), 'numero_operacion': '5695993281717', 'msisdn': '51931538578', 'nro_suministro': '82751884', 'registro': 'PEA716C13333082025-06-1208.33.55993116590000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('6.00')}, {'financial_id': Decimal('1162506120834322633254'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 34, 31), 'numero_operacion': '5695993281735', 'msisdn': '51975900653', 'nro_suministro': '08652187', 'registro': 'PEA716C13341942025-06-1208.34.32993216600000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('78.70')}, {'financial_id': Decimal('5902506120836090363530'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 36, 8), 'numero_operacion': '5695993281770', 'msisdn': '51986066684', 'nro_suministro': '959047392', 'registro': 'PEA716C13351782025-06-1208.36.09993316610000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('40.90')}, {'financial_id': Decimal('1162506120836164733365'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 36, 16), 'numero_operacion': '5695993281773', 'msisdn': '51927609730', 'nro_suministro': '10046110', 'registro': 'PEA716C13355482025-06-1208.36.17993416630000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('21.90')}, {'financial_id': Decimal('1162506120842374393710'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 42, 36), 'numero_operacion': '5695993282052', 'msisdn': '51951933341', 'nro_suministro': '987510815', 'registro': 'PEA716C13420932025-06-1208.42.38993516640000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('8502506120846294813391'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 46, 28), 'numero_operacion': '5695993282135', 'msisdn': '51900829082', 'nro_suministro': '25786273', 'registro': 'PEA716C13461052025-06-1208.46.30993616660000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('68.50')}, {'financial_id': Decimal('8502506120853348283835'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 53, 34), 'numero_operacion': '5695993282431', 'msisdn': '51981266078', 'nro_suministro': '42523477', 'registro': 'PEA716C13522862025-06-1208.53.35993716670000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EDPYME_ProEmpresa@WU', 'service_charge': Decimal('1.50'), 'total': Decimal('213.70')}, {'financial_id': Decimal('8502506120853368903840'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 53, 36), 'numero_operacion': '5695993282500', 'msisdn': '51928278907', 'nro_suministro': '10091023', 'registro': 'PEA716C13531752025-06-1208.53.37993816690000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('8502506120854014423864'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 54, 1), 'numero_operacion': '5695993282510', 'msisdn': '51928278907', 'nro_suministro': '10091023', 'registro': 'PEA716C13534912025-06-1208.54.02993916700000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 54, 3), 'numero_operacion': '0', 'msisdn': '51967159803', 'nro_suministro': '41843633 ', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EDPYME_ProEmpresa@WU', 'service_charge': Decimal('1.50'), 'total': Decimal('387.90')}, {'financial_id': Decimal('1162506120854497794442'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 54, 49), 'numero_operacion': '5695993282576', 'msisdn': '51928278907', 'nro_suministro': '10091023', 'registro': 'PEA716C13540922025-06-1208.54.50994016710000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 55, 10), 'numero_operacion': '0', 'msisdn': '51967159803', 'nro_suministro': '41843633', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EDPYME_ProEmpresa@WU', 'service_charge': Decimal('1.50'), 'total': Decimal('387.90')}, {'financial_id': Decimal('5902506120855134474626'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 55, 13), 'numero_operacion': '5695993282581', 'msisdn': '51981266078', 'nro_suministro': '42523477', 'registro': 'PEA716C13543532025-06-1208.55.14994116720000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EDPYME_ProEmpresa@WU', 'service_charge': Decimal('1.50'), 'total': Decimal('213.70')}, {'financial_id': Decimal('5902506120855233004641'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 55, 22), 'numero_operacion': '5695993282593', 'msisdn': '51928278907', 'nro_suministro': '70977388', 'registro': 'PEA716C13545912025-06-1208.55.23994216740000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('1162506120855532054514'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 55, 52), 'numero_operacion': '5695993282639', 'msisdn': '51928278907', 'nro_suministro': '70977388', 'registro': 'PEA716C13553182025-06-1208.55.53994316750000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 57, 1), 'numero_operacion': '0', 'msisdn': '51967159803', 'nro_suministro': '41843633', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EDPYME_ProEmpresa@WU', 'service_charge': Decimal('1.50'), 'total': Decimal('387.90')}, {'financial_id': Decimal('1162506120857421104607'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 57, 41), 'numero_operacion': '5695993282642', 'msisdn': '51928278907', 'nro_suministro': '70977388', 'registro': 'PEA716C13560712025-06-1208.57.42994416760000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('8502506120857429954113'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 57, 42), 'numero_operacion': '5695993282655', 'msisdn': '51910776247', 'nro_suministro': '1626775', 'registro': 'PEA716C13571692025-06-1208.57.44994516770000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('913.60')}, {'financial_id': Decimal('1162506120859385924728'), 'payment_time': datetime.datetime(2025, 6, 12, 8, 59, 38), 'numero_operacion': '5695993282752', 'msisdn': '51967159803', 'nro_suministro': '41843633 ', 'registro': 'PEA716C13591792025-06-1208.59.39994616780000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EDPYME_ProEmpresa@WU', 'service_charge': Decimal('1.50'), 'total': Decimal('387.90')}, {'financial_id': Decimal('8502506120900437614292'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 0, 43), 'numero_operacion': '5695993282828', 'msisdn': '51907020724', 'nro_suministro': '907020724', 'registro': 'PEA716C14001632025-06-1209.00.44994716800000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('37.14')}, {'financial_id': Decimal('5902506120900583024978'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 0, 57), 'numero_operacion': '5695993282819', 'msisdn': '51956856570', 'nro_suministro': '956856570', 'registro': 'PEA716C13592242025-06-1209.00.59994816820000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('29.40')}, {'financial_id': Decimal('5902506120903028185100'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 3, 2), 'numero_operacion': '5695993282913', 'msisdn': '51960071606', 'nro_suministro': '928137196', 'registro': 'PEA716C14022662025-06-1209.03.03994916840000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506120911318495545'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 11, 31), 'numero_operacion': '5695993283110', 'msisdn': '51945669245', 'nro_suministro': '15738648', 'registro': 'PEA716C14105102025-06-1209.11.32995016860000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('32.30')}, {'financial_id': Decimal('1162506120911510315561'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 11, 50), 'numero_operacion': '5695993283115', 'msisdn': '51961271739', 'nro_suministro': '961271739', 'registro': 'PEA716C14112572025-06-1209.11.51995116870000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('29.10')}, {'financial_id': Decimal('5902506120913574315859'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 13, 57), 'numero_operacion': '5695993283264', 'msisdn': '51979505238', 'nro_suministro': '979505238', 'registro': 'PEA716C14133512025-06-1209.13.58995216890000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('45.00')}, {'financial_id': Decimal('8502506120916008985331'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 16), 'numero_operacion': '5695993283272', 'msisdn': '51991942729', 'nro_suministro': '039412829', 'registro': 'PEA716C14152972025-06-1209.16.02995316910000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('199.98')}, {'financial_id': Decimal('1162506120923046566293'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 23, 4), 'numero_operacion': '5695993283683', 'msisdn': '51990764102', 'nro_suministro': '951899466', 'registro': 'PEA716C14223022025-06-1209.23.05995416920000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('1162506120923317716323'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 23, 31), 'numero_operacion': '5695993283684', 'msisdn': '51908641605', 'nro_suministro': '988031329', 'registro': 'PEA716C14224592025-06-1209.23.32995516940000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('8502506120931018746369'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 31, 1), 'numero_operacion': '5695993284027', 'msisdn': '51925332199', 'nro_suministro': '62452510', 'registro': 'PEA716C14304172025-06-1209.31.02995616960000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('35.70')}, {'financial_id': Decimal('8502506120933228056558'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 33, 22), 'numero_operacion': '5695993284174', 'msisdn': '51981285051', 'nro_suministro': '39685218 ', 'registro': 'PEA716C14325642025-06-1209.33.24995716970000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('143.20')}, {'financial_id': Decimal('5902506120933477907195'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 33, 47), 'numero_operacion': '5695993284206', 'msisdn': '51921440774', 'nro_suministro': '5530072', 'registro': 'PEA716C14331652025-06-1209.33.48995816980000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('69.10')}, {'financial_id': Decimal('5902506120934574877279'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 34, 57), 'numero_operacion': '5695993284248', 'msisdn': '51921440774', 'nro_suministro': '1920277', 'registro': 'PEA716C14343522025-06-1209.34.58995917000000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('64.00')}, {'financial_id': Decimal('8502506120942355027224'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 42, 34), 'numero_operacion': '5695993284555', 'msisdn': '51945669245', 'nro_suministro': '15738648', 'registro': 'PEA716C14421682025-06-1209.42.36996017010000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506120946351637956'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 46, 34), 'numero_operacion': '5695993284771', 'msisdn': '51921440774', 'nro_suministro': '3832216', 'registro': 'PEA716C14461412025-06-1209.46.36996117020000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('20.30')}, {'financial_id': Decimal('1162506120947470468052'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 47, 46), 'numero_operacion': '5695993284824', 'msisdn': '51956775068', 'nro_suministro': '053121306', 'registro': 'PEA716C14471352025-06-1209.47.47996217040000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('380.07')}, {'financial_id': Decimal('5902506120952100158547'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 52, 9), 'numero_operacion': '5695993285036', 'msisdn': '51974246892', 'nro_suministro': '049877358', 'registro': 'PEA716C14513692025-06-1209.52.10996317050000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('244.32')}, {'financial_id': Decimal('5902506120953058008619'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 53, 5), 'numero_operacion': '5695993285052', 'msisdn': '51980023328', 'nro_suministro': '046447654', 'registro': 'PEA716C14522872025-06-1209.53.06996417060000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('203.81')}, {'financial_id': Decimal('1162506120955023818567'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 55, 1), 'numero_operacion': '5695993285046', 'msisdn': '51955351162', 'nro_suministro': '18742537', 'registro': 'PEA716C14520862025-06-1209.55.03996517070000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('100.30')}, {'financial_id': Decimal('1162506120955518548627'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 55, 51), 'numero_operacion': '5695993285240', 'msisdn': '51997116147', 'nro_suministro': '5142971 ', 'registro': 'PEA716C14552052025-06-1209.55.52996617080000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('19.80')}, {'financial_id': Decimal('5902506120958534629048'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 58, 53), 'numero_operacion': '5695993285356', 'msisdn': '51932928461', 'nro_suministro': '939845370', 'registro': 'PEA716C14583602025-06-1209.58.54996717090000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('53.10')}, {'financial_id': Decimal('8502506120959452388428'), 'payment_time': datetime.datetime(2025, 6, 12, 9, 59, 44), 'numero_operacion': '5695993285372', 'msisdn': '51997116147', 'nro_suministro': '1676992 ', 'registro': 'PEA716C14591272025-06-1209.59.45996817110000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('28.50')}, {'financial_id': Decimal('1162506121000110268973'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 0, 10), 'numero_operacion': '5695993285363', 'msisdn': '51933228709', 'nro_suministro': '49075642', 'registro': 'PEA716C14590582025-06-1210.00.11996917120000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EDPYME_ProEmpresa@WU', 'service_charge': Decimal('1.50'), 'total': Decimal('635.20')}, {'financial_id': Decimal('8502506121000181648470'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 0, 17), 'numero_operacion': '5695993285375', 'msisdn': '51921440774', 'nro_suministro': '5615909', 'registro': 'PEA716C14595192025-06-1210.00.19997017140000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('17.30')}, {'financial_id': Decimal('1162506121000550469032'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 0, 54), 'numero_operacion': '5695993285400', 'msisdn': '51931627109', 'nro_suministro': '053393926', 'registro': 'PEA716C15004072025-06-1210.00.55997117160000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('17.00')}, {'financial_id': Decimal('5902506121001326839236'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 1, 32), 'numero_operacion': '5695993285402', 'msisdn': '51997116147', 'nro_suministro': '5257387 ', 'registro': 'PEA716C15010932025-06-1210.01.33997217170000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('27.70')}, {'financial_id': Decimal('5902506121006435019616'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 6, 43), 'numero_operacion': '5695993285645', 'msisdn': '51968785328', 'nro_suministro': '10737755', 'registro': 'PEA716C15062832025-06-1210.06.44997317190000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('69.40')}, {'financial_id': Decimal('1162506121009120219769'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 9, 11), 'numero_operacion': '5695993285719', 'msisdn': '51949345168', 'nro_suministro': '17859615', 'registro': 'PEA716C15085872025-06-1210.09.12997417200000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('52.20')}, {'financial_id': Decimal('8502506121011416099457'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 11, 41), 'numero_operacion': '5695993285797', 'msisdn': '51980981339', 'nro_suministro': '16323002', 'registro': 'PEA716C15112132025-06-1210.11.42997517210000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('15.90')}, {'financial_id': Decimal('5902506121012415790230'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 12, 41), 'numero_operacion': '5695993285841', 'msisdn': '51980981339', 'nro_suministro': '16323002', 'registro': 'PEA716C15122382025-06-1210.12.42997617220000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('11.20')}, {'financial_id': Decimal('1162506121013023420077'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 13, 1), 'numero_operacion': '5695993285801', 'msisdn': '51996301464', 'nro_suministro': '17630732', 'registro': 'PEA716C15112752025-06-1210.13.03997717230000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('64.30')}, {'financial_id': Decimal('5902506121013135520273'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 13, 13), 'numero_operacion': '5695993285853', 'msisdn': '51927493069', 'nro_suministro': '10956420', 'registro': 'PEA716C15124892025-06-1210.13.14997817240000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('45.80')}, {'financial_id': Decimal('5902506121015340410445'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 15, 33), 'numero_operacion': '5695993285894', 'msisdn': '51948295210', 'nro_suministro': '18234547', 'registro': 'PEA716C15144122025-06-1210.15.34997917250000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('448.60')}, {'financial_id': Decimal('8502506121020455880237'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 20, 45), 'numero_operacion': '5695993286006', 'msisdn': '51922299017', 'nro_suministro': '49290443', 'registro': 'PEA716C15195642025-06-1210.20.46998017260000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('138.90')}, {'financial_id': Decimal('5902506121027259481445'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 27, 25), 'numero_operacion': '5695993286232', 'msisdn': '51937044631', 'nro_suministro': '937044631 ', 'registro': 'PEA716C15270852025-06-1210.27.26998117270000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('14.10')}, {'financial_id': Decimal('8502506121027434740819'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 27, 43), 'numero_operacion': '5695993286296', 'msisdn': '51998496290', 'nro_suministro': '2010010647', 'registro': 'PEA716C15272752025-06-1210.27.44998217290000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electro_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.60')}, {'financial_id': Decimal('8502506121029159480948'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 29, 15), 'numero_operacion': '5695993286360', 'msisdn': '51998496290', 'nro_suministro': '2010009343', 'registro': 'PEA716C15285842025-06-1210.29.16998317300000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electro_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('6.80')}, {'financial_id': Decimal('1162506121029212131539'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 29, 20), 'numero_operacion': '5695993286361', 'msisdn': '51923743196', 'nro_suministro': '10010667', 'registro': 'PEA716C15290192025-06-1210.29.21998417310000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EMSA_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('5.50')}, {'financial_id': Decimal('1162506121033051131856'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 33, 4), 'numero_operacion': '5695993286464', 'msisdn': '51998496290', 'nro_suministro': '10127702', 'registro': 'PEA716C15325292025-06-1210.33.05998517320000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electro_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.50')}, {'financial_id': Decimal('8502506121034204851343'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 34, 20), 'numero_operacion': '5695993286525', 'msisdn': '51998496290', 'nro_suministro': '10042983', 'registro': 'PEA716C15340762025-06-1210.34.21998617330000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EMSA_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('5902506121036243422201'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 36, 23), 'numero_operacion': '5695993286604', 'msisdn': '51968785328', 'nro_suministro': '11264105', 'registro': 'PEA716C15360462025-06-1210.36.24998717340000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('33.90')}, {'financial_id': Decimal('1162506121037175072179'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 37, 17), 'numero_operacion': '5695993286645', 'msisdn': '51943382793', 'nro_suministro': '943382793', 'registro': 'PEA716C15364512025-06-1210.37.18998817350000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('5902506121039157332465'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 39, 15), 'numero_operacion': '5695993286650', 'msisdn': '51907041661', 'nro_suministro': '907041661 ', 'registro': 'PEA716C15365772025-06-1210.39.16998917370000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.50')}, {'financial_id': Decimal('8502506121040492991919'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 40, 48), 'numero_operacion': '5695993286753', 'msisdn': '51999077581', 'nro_suministro': '968498347', 'registro': 'PEA716C15403242025-06-1210.40.50999017390000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('37.30')}, {'financial_id': Decimal('1162506121041598222596'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 41, 59), 'numero_operacion': '5695993286821', 'msisdn': '51951068833', 'nro_suministro': '951068833', 'registro': 'PEA716C15411732025-06-1210.42.00999117410000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('5902506121044340352875'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 44, 33), 'numero_operacion': '5695993286904', 'msisdn': '51939304353', 'nro_suministro': '939304353', 'registro': 'PEA716C15441712025-06-1210.44.34999217430000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('45.00')}, {'financial_id': Decimal('8502506121044431962223'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 44, 42), 'numero_operacion': '5695993286906', 'msisdn': '51998496290', 'nro_suministro': '10028593', 'registro': 'PEA716C15442862025-06-1210.44.43999317450000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electro_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('17.80')}, {'financial_id': Decimal('5902506121044472542894'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 44, 46), 'numero_operacion': '5695993286903', 'msisdn': '51917050848', 'nro_suministro': '15310289', 'registro': 'PEA716C15441392025-06-1210.44.47999417460000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('42.40')}, {'financial_id': Decimal('5902506121045579682997'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 45, 57), 'numero_operacion': '5695993286951', 'msisdn': '51998496290', 'nro_suministro': '10030537', 'registro': 'PEA716C15453772025-06-1210.45.58999517470000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EMSA_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('11.00')}, {'financial_id': Decimal('8502506121048496572567'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 48, 49), 'numero_operacion': '5695993287023', 'msisdn': '51929414150', 'nro_suministro': '86859563', 'registro': 'PEA716C15481852025-06-1210.48.50999617480000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('29.60')}, {'financial_id': Decimal('5902506121049373003350'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 49, 36), 'numero_operacion': '5695993287034', 'msisdn': '51929414150', 'nro_suministro': '86859563', 'registro': 'PEA716C15491892025-06-1210.49.37999717490000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('25.90')}, {'financial_id': Decimal('5902506121050129073405'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 50, 12), 'numero_operacion': '5695993287081', 'msisdn': '51929414150', 'nro_suministro': '86859563', 'registro': 'PEA716C15495702025-06-1210.50.13999817500000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.00')}, {'financial_id': Decimal('1162506121051361433400'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 51, 35), 'numero_operacion': '5695993287139', 'msisdn': '51929414150', 'nro_suministro': '89460118', 'registro': 'PEA716C15511732025-06-1210.51.36999917510000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('9.00')}, {'financial_id': Decimal('1162506121054056713582'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 54, 5), 'numero_operacion': '5695993287257', 'msisdn': '51931136667', 'nro_suministro': '66944424', 'registro': 'PEA716C15534972025-06-1210.54.06000117520000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('6.60')}, {'financial_id': Decimal('1162506121054377603614'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 54, 37), 'numero_operacion': '5695993287278', 'msisdn': '51931136667', 'nro_suministro': '47916679', 'registro': 'PEA716C15542682025-06-1210.54.38000217530000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.60')}, {'financial_id': Decimal('8502506121055227703110'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 55, 22), 'numero_operacion': '5695993287281', 'msisdn': '51931136667', 'nro_suministro': '61558309', 'registro': 'PEA716C15545072025-06-1210.55.23000317540000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('8.10')}, {'financial_id': Decimal('1162506121055493243704'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 55, 48), 'numero_operacion': '5695993287321', 'msisdn': '51931136667', 'nro_suministro': '61558309', 'registro': 'PEA716C15553842025-06-1210.55.50000417550000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.80')}, {'financial_id': Decimal('1162506121056196103757'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 56, 19), 'numero_operacion': '5695993287331', 'msisdn': '51931136667', 'nro_suministro': '47931871', 'registro': 'PEA716C15560762025-06-1210.56.20000517560000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('11.00')}, {'financial_id': Decimal('5902506121057157564009'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 57, 15), 'numero_operacion': '5695993287346', 'msisdn': '51952796461', 'nro_suministro': '47609097', 'registro': 'PEA716C15565752025-06-1210.57.16000617570000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('123.20')}, {'financial_id': Decimal('1162506121057470093893'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 57, 46), 'numero_operacion': '5695993287377', 'msisdn': '51919159632', 'nro_suministro': '50353439', 'registro': 'PEA716C15573082025-06-1210.57.47000717580000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('90.60')}, {'financial_id': Decimal('8502506121058462223411'), 'payment_time': datetime.datetime(2025, 6, 12, 10, 58, 45), 'numero_operacion': '5695993287378', 'msisdn': '51973707020', 'nro_suministro': '27753692', 'registro': 'PEA716C15573322025-06-1210.58.47000817590000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('50.40')}, {'financial_id': Decimal('1162506121100349664141'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 0, 34), 'numero_operacion': '5695993287468', 'msisdn': '51912094301', 'nro_suministro': '11089650', 'registro': 'PEA716C16001102025-06-1211.00.35000917600000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.10')}, {'financial_id': Decimal('1162506121103048624384'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 3, 4), 'numero_operacion': '5695993287578', 'msisdn': '51947418516', 'nro_suministro': '49450888', 'registro': 'PEA716C16024812025-06-1211.03.05001017610000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('130.10')}, {'financial_id': Decimal('1162506121111471355140'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 11, 46), 'numero_operacion': '5695993287813', 'msisdn': '51945619750', 'nro_suministro': '945619750', 'registro': 'PEA716C16105292025-06-1211.11.47001117620000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.00')}, {'financial_id': Decimal('8502506121111537314531'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 11, 53), 'numero_operacion': '5695993287812', 'msisdn': '51976765956', 'nro_suministro': '63329520', 'registro': 'PEA716C16105622025-06-1211.11.54001217640000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('92.20')}, {'financial_id': Decimal('8502506121117129514921'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 17, 12), 'numero_operacion': '5695993287971', 'msisdn': '51944464113', 'nro_suministro': '944464113', 'registro': 'PEA716C16165132025-06-1211.17.13001317650000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('44.60')}, {'financial_id': Decimal('5902506121118301105852'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 18, 29), 'numero_operacion': '5695993287969', 'msisdn': '51903510710', 'nro_suministro': '6690008', 'registro': 'PEA716C16165002025-06-1211.18.30001417670000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('32.50')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 19, 55), 'numero_operacion': '0', 'msisdn': '51984725748', 'nro_suministro': '8700954', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('100.00')}, {'financial_id': Decimal('5902506121119582875965'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 19, 57), 'numero_operacion': '5695993288061', 'msisdn': '51933159859', 'nro_suministro': '901035721', 'registro': 'PEA716C16192842025-06-1211.19.58001517680000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.10')}, {'financial_id': Decimal('8502506121120075915137'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 20, 7), 'numero_operacion': '5695993288062', 'msisdn': '51903510710', 'nro_suministro': '6690008', 'registro': 'PEA716C16193982025-06-1211.20.08001617700000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('47.40')}, {'financial_id': Decimal('5902506121120176845998'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 20, 17), 'numero_operacion': '5695993288067', 'msisdn': '51984725748', 'nro_suministro': '8700954', 'registro': 'PEA716C16200772025-06-1211.20.18001717710000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('100.00')}, {'financial_id': Decimal('8502506121120384725177'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 20, 38), 'numero_operacion': '5695993288064', 'msisdn': '51996539883', 'nro_suministro': '047044804', 'registro': 'PEA716C16200012025-06-1211.20.39001817720000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('13.30')}, {'financial_id': Decimal('1162506121120579415887'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 20, 57), 'numero_operacion': '5695993288072', 'msisdn': '51945868995', 'nro_suministro': '920909245', 'registro': 'PEA716C16203302025-06-1211.20.58001917730000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('5902506121122355286215'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 22, 35), 'numero_operacion': '5695993288202', 'msisdn': '51947037447', 'nro_suministro': '10733765', 'registro': 'PEA716C16222432025-06-1211.22.36002017750000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.10')}, {'financial_id': Decimal('5902506121122419836228'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 22, 41), 'numero_operacion': '5695993288203', 'msisdn': '51933632626', 'nro_suministro': '933632626', 'registro': 'PEA716C16222762025-06-1211.22.42002117760000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('5902506121124294186374'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 24, 29), 'numero_operacion': '5695993288277', 'msisdn': '51981098437', 'nro_suministro': '74900890', 'registro': 'PEA716C16235212025-06-1211.24.30002217780000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('14.40')}, {'financial_id': Decimal('5902506121126196776525'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 26, 19), 'numero_operacion': '5695993288291', 'msisdn': '51969638522', 'nro_suministro': '969638522', 'registro': 'PEA716C16255952025-06-1211.26.20002317790000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('46.10')}, {'financial_id': Decimal('1162506121128067146524'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 28, 6), 'numero_operacion': '5695993288381', 'msisdn': '51907840764', 'nro_suministro': '924452182', 'registro': 'PEA716C16272222025-06-1211.28.07002417810000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('128.91')}, {'financial_id': Decimal('8502506121128139395846'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 28, 13), 'numero_operacion': '5695993288384', 'msisdn': '51996539883', 'nro_suministro': '047044804', 'registro': 'PEA716C16273852025-06-1211.28.14002517830000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('200.00')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 28, 16), 'numero_operacion': '0', 'msisdn': '51938610431', 'nro_suministro': '938610431', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('45.40')}, {'financial_id': Decimal('1162506121128428796573'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 28, 42), 'numero_operacion': '5695993288457', 'msisdn': '51938610431', 'nro_suministro': '938610431', 'registro': 'PEA716C16283022025-06-1211.28.43002617840000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('45.40')}, {'financial_id': Decimal('8502506121130325826029'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 30, 32), 'numero_operacion': '5695993288468', 'msisdn': '51958362622', 'nro_suministro': '1200014', 'registro': 'PEA716C16301362025-06-1211.30.33002717860000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('13.50')}, {'financial_id': Decimal('1162506121132188826870'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 32, 18), 'numero_operacion': '5695993288474', 'msisdn': '51920652492', 'nro_suministro': '920652492', 'registro': 'PEA716C16314202025-06-1211.32.19002817870000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('52.80')}, {'financial_id': Decimal('5902506121133004777110'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 33), 'numero_operacion': '5695993288568', 'msisdn': '51920652492', 'nro_suministro': '920652492', 'registro': 'PEA716C16324732025-06-1211.33.01002917890000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('50.50')}, {'financial_id': Decimal('1162506121134440517069'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 34, 43), 'numero_operacion': '5695993288616', 'msisdn': '51956087799', 'nro_suministro': '034129908', 'registro': 'PEA716C16335942025-06-1211.34.44003017910000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('114.35')}, {'financial_id': Decimal('8502506121135459646468'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 35, 45), 'numero_operacion': '5695993288632', 'msisdn': '51999110915', 'nro_suministro': '951912925', 'registro': 'PEA716C16353242025-06-1211.35.46003117920000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('71.30')}, {'financial_id': Decimal('8502506121137019726578'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 37, 1), 'numero_operacion': '5695993288682', 'msisdn': '51981564412', 'nro_suministro': '55066827', 'registro': 'PEA716C16363792025-06-1211.37.02003217940000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('300.00')}, {'financial_id': Decimal('1162506121140060517509'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 40, 5), 'numero_operacion': '5695993288736', 'msisdn': '51941362000', 'nro_suministro': '941362000', 'registro': 'PEA716C16374292025-06-1211.40.06003317950000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('51.10')}, {'financial_id': Decimal('5902506121147135188259'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 47, 12), 'numero_operacion': '5695993288936', 'msisdn': '51924219678', 'nro_suministro': '1974665', 'registro': 'PEA716C16464572025-06-1211.47.14003417970000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.00')}, {'financial_id': Decimal('1162506121150517958380'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 50, 51), 'numero_operacion': '5695993289018', 'msisdn': '51903080125', 'nro_suministro': '83643059', 'registro': 'PEA716C16501112025-06-1211.50.52003517980000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.00')}, {'financial_id': Decimal('5902506121151216018601'), 'payment_time': datetime.datetime(2025, 6, 12, 11, 51, 21), 'numero_operacion': '5695993289030', 'msisdn': '51996539883', 'nro_suministro': '047044804', 'registro': 'PEA716C16510292025-06-1211.51.22003617990000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('25.00')}, {'financial_id': Decimal('1162506121204512909483'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 4, 50), 'numero_operacion': '5695993289353', 'msisdn': '51954632823', 'nro_suministro': '954632823 ', 'registro': 'PEA716C17042132025-06-1212.04.51003718000000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('36.10')}, {'financial_id': Decimal('8502506121204540508853'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 4, 53), 'numero_operacion': '5695993289397', 'msisdn': '51976855925', 'nro_suministro': '26068155', 'registro': 'PEA716C17043952025-06-1212.04.54003818020000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('45.40')}, {'financial_id': Decimal('5902506121204552359692'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 4, 54), 'numero_operacion': '5695993289399', 'msisdn': '51927769831', 'nro_suministro': '1646608', 'registro': 'PEA716C17044112025-06-1212.04.55003918030000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('205.50')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 5, 33), 'numero_operacion': '0', 'msisdn': '51918645823', 'nro_suministro': '15967328', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('108.70')}, {'financial_id': Decimal('5902506121206210009793'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 6, 20), 'numero_operacion': '5695993289412', 'msisdn': '51918645823', 'nro_suministro': '15967328', 'registro': 'PEA716C17055082025-06-1212.06.21004018040000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('108.70')}, {'financial_id': Decimal('5902506121209162100050'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 9, 15), 'numero_operacion': '5695993289516', 'msisdn': '51996539883', 'nro_suministro': '047044804', 'registro': 'PEA716C17090072025-06-1212.09.16004118050000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('176.59')}, {'financial_id': Decimal('8502506121210452849314'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 10, 44), 'numero_operacion': '5695993289530', 'msisdn': '51976855925', 'nro_suministro': '35848389', 'registro': 'PEA716C17103142025-06-1212.10.46004218060000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('215.80')}, {'financial_id': Decimal('5902506121215517680625'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 15, 51), 'numero_operacion': '5695993289634', 'msisdn': '51945331604', 'nro_suministro': '968486213', 'registro': 'PEA716C17151372025-06-1212.15.52004318070000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('9.10')}, {'financial_id': Decimal('1162506121216369670499'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 16, 36), 'numero_operacion': '5695993289679', 'msisdn': '51960200856', 'nro_suministro': '900828540', 'registro': 'PEA716C17161292025-06-1212.16.37004418090000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('81.10')}, {'financial_id': Decimal('1162506121222077800940'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 22, 7), 'numero_operacion': '5695993289783', 'msisdn': '51968193574', 'nro_suministro': '10048920', 'registro': 'PEA716C17213722025-06-1212.22.08004518110000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('31.40')}, {'financial_id': Decimal('8502506121223412160355'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 23, 40), 'numero_operacion': '5695993289818', 'msisdn': '51920835040', 'nro_suministro': '7821623', 'registro': 'PEA716C17225182025-06-1212.23.41004618120000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('23.20')}, {'financial_id': Decimal('5902506121223469121259'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 23, 46), 'numero_operacion': '0', 'msisdn': '51942991231', 'nro_suministro': '942991231', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('46.35')}, {'financial_id': Decimal('1162506121224127771085'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 24, 12), 'numero_operacion': '0', 'msisdn': '51942991231', 'nro_suministro': '942991231', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('46.35')}, {'financial_id': Decimal('5902506121224417761354'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 24, 41), 'numero_operacion': '0', 'msisdn': '51942991231', 'nro_suministro': '942991231', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('46.35')}, {'financial_id': Decimal('8502506121225127950497'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 25, 12), 'numero_operacion': '5695993289860', 'msisdn': '51920835040', 'nro_suministro': '7821623', 'registro': 'PEA716C17242722025-06-1212.25.13005018130000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('8.30')}, {'financial_id': Decimal('1162506121225520521237'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 25, 51), 'numero_operacion': '5695993289885', 'msisdn': '51983084773', 'nro_suministro': '11069254', 'registro': 'PEA716C17253862025-06-1212.25.52005118140000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('33.90')}, {'financial_id': Decimal('1162506121226182341272'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 26, 17), 'numero_operacion': '0', 'msisdn': '51942991231', 'nro_suministro': '942991231', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('46.35')}, {'financial_id': Decimal('5902506121226388291516'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 26, 38), 'numero_operacion': '5695993289919', 'msisdn': '51946501772', 'nro_suministro': '051593502', 'registro': 'PEA716C17262032025-06-1212.26.39005318150000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('148.00')}, {'financial_id': Decimal('8502506121227548840727'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 27, 54), 'numero_operacion': '5695993289926', 'msisdn': '51976134182', 'nro_suministro': '7764661', 'registro': 'PEA716C17272682025-06-1212.27.55005418160000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('89.40')}, {'financial_id': Decimal('5902506121231008111889'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 31), 'numero_operacion': '5695993289965', 'msisdn': '51943364219', 'nro_suministro': '944754686', 'registro': 'PEA716C17292862025-06-1212.31.01005518170000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('34.90')}, {'financial_id': Decimal('1162506121234081041912'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 34, 7), 'numero_operacion': '5695993290026', 'msisdn': '51918319643', 'nro_suministro': '918319643', 'registro': 'PEA716C17332742025-06-1212.34.08005618190000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.10')}, {'financial_id': Decimal('8502506121238053231601'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 38, 4), 'numero_operacion': '5695993290093', 'msisdn': '51934722671', 'nro_suministro': '951677368', 'registro': 'PEA716C17363022025-06-1212.38.06005718210000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('33.10')}, {'financial_id': Decimal('5902506121239059202576'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 39, 5), 'numero_operacion': '5695993290160', 'msisdn': '51912967106', 'nro_suministro': '912967106 ', 'registro': 'PEA716C17383912025-06-1212.39.06005818230000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('22.05')}, {'financial_id': Decimal('1162506121244486002778'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 44, 48), 'numero_operacion': '5695993290289', 'msisdn': '51951362896', 'nro_suministro': '972630249', 'registro': 'PEA716C17441942025-06-1212.44.49005918250000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('24.20')}, {'financial_id': Decimal('5902506121249249673412'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 49, 24), 'numero_operacion': '5695993290416', 'msisdn': '51978744789', 'nro_suministro': '50654030', 'registro': 'PEA716C17485912025-06-1212.49.25006018270000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('23.60')}, {'financial_id': Decimal('8502506121250103232585'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 50, 9), 'numero_operacion': '5695993290374', 'msisdn': '51946501772', 'nro_suministro': '051593502', 'registro': 'PEA716C17484622025-06-1212.50.11006118280000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('45.00')}, {'financial_id': Decimal('1162506121251443143346'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 51, 43), 'numero_operacion': '5695993290461', 'msisdn': '51978744789', 'nro_suministro': '58124924', 'registro': 'PEA716C17512522025-06-1212.51.45006218290000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('68.90')}, {'financial_id': Decimal('8502506121252380392784'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 52, 37), 'numero_operacion': '5695993290468', 'msisdn': '51937742475', 'nro_suministro': '912437426', 'registro': 'PEA716C17515692025-06-1212.52.38006318300000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('34.30')}, {'financial_id': Decimal('1162506121253089753460'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 53, 8), 'numero_operacion': '5695993290474', 'msisdn': '51966271780', 'nro_suministro': '966271780', 'registro': 'PEA716C17521652025-06-1212.53.09006418320000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506121254211503548'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 54, 20), 'numero_operacion': '5695993290457', 'msisdn': '51929182464', 'nro_suministro': '932598814', 'registro': 'PEA716C17505452025-06-1212.54.21006518340000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506121254526603585'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 54, 52), 'numero_operacion': '5695993290512', 'msisdn': '51951474141', 'nro_suministro': '10044875', 'registro': 'PEA716C17543512025-06-1212.54.53006618360000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EMSA_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('21.60')}, {'financial_id': Decimal('1162506121254577983598'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 54, 57), 'numero_operacion': '5695993290510', 'msisdn': '51955450076', 'nro_suministro': '8611845', 'registro': 'PEA716C17543442025-06-1212.54.58006718370000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('220.60')}, {'financial_id': Decimal('5902506121258153394140'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 58, 14), 'numero_operacion': '5695993290617', 'msisdn': '51951474141', 'nro_suministro': '10030004', 'registro': 'PEA716C17575222025-06-1212.58.15006818380000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EMSA_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('6.10')}, {'financial_id': Decimal('5902506121259407014255'), 'payment_time': datetime.datetime(2025, 6, 12, 12, 59, 40), 'numero_operacion': '5695993290625', 'msisdn': '51948024497', 'nro_suministro': '948024497', 'registro': 'PEA716C17591312025-06-1212.59.41006918390000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('5902506121300129334296'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 0, 12), 'numero_operacion': '5695993290627', 'msisdn': '51944066493', 'nro_suministro': '961009040', 'registro': 'PEA716C17594872025-06-1213.00.13007018410000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('80.90')}, {'financial_id': Decimal('8502506121303195023705'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 3, 19), 'numero_operacion': '5695993290693', 'msisdn': '51918010944', 'nro_suministro': '10270780', 'registro': 'PEA716C18030102025-06-1213.03.20007118430000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('36.80')}, {'financial_id': Decimal('1162506121304063734343'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 4, 6), 'numero_operacion': '5695993290760', 'msisdn': '51918010944', 'nro_suministro': '10270780', 'registro': 'PEA716C18035192025-06-1213.04.07007218440000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('41.50')}, {'financial_id': Decimal('1162506121305173374440'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 5, 16), 'numero_operacion': '5695993290766', 'msisdn': '51903540369', 'nro_suministro': '36988769', 'registro': 'PEA716C18043412025-06-1213.05.18007318450000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('43.90')}, {'financial_id': Decimal('8502506121306404193961'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 6, 40), 'numero_operacion': '5695993290775', 'msisdn': '51984260116', 'nro_suministro': '984260116', 'registro': 'PEA716C18055482025-06-1213.06.41007418460000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('51.10')}, {'financial_id': Decimal('5902506121306447314822'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 6, 44), 'numero_operacion': '5695993290844', 'msisdn': '51949345168', 'nro_suministro': '10806093', 'registro': 'PEA716C18063232025-06-1213.06.45007518480000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('8.00')}, {'financial_id': Decimal('8502506121307179474011'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 7, 17), 'numero_operacion': '5695993290853', 'msisdn': '51949345168', 'nro_suministro': '10806093', 'registro': 'PEA716C18070742025-06-1213.07.18007618490000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.30')}, {'financial_id': Decimal('5902506121312423525307'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 12, 41), 'numero_operacion': '5695993290970', 'msisdn': '51955450076', 'nro_suministro': '8625804', 'registro': 'PEA716C18121292025-06-1213.12.43007718500000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('141.00')}, {'financial_id': Decimal('8502506121314351804582'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 14, 34), 'numero_operacion': '5695993291085', 'msisdn': '51984294627', 'nro_suministro': '89375670', 'registro': 'PEA716C18140992025-06-1213.14.35007818510000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('36.70')}, {'financial_id': Decimal('1162506121316159805297'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 16, 15), 'numero_operacion': '5695993291122', 'msisdn': '51984294627', 'nro_suministro': '89375670', 'registro': 'PEA716C18155652025-06-1213.16.16007918520000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('35.40')}, {'financial_id': Decimal('1162506121326508636131'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 26, 50), 'numero_operacion': '5695993291478', 'msisdn': '51951445451', 'nro_suministro': '0677652', 'registro': 'PEA716C18263032025-06-1213.26.51008018530000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('226.50')}, {'financial_id': Decimal('1162506121328573256300'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 28, 56), 'numero_operacion': '5695993291487', 'msisdn': '51951445451', 'nro_suministro': '0629508', 'registro': 'PEA716C18281752025-06-1213.28.58008118540000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('59.50')}, {'financial_id': Decimal('5902506121329146196618'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 29, 14), 'numero_operacion': '5695993291488', 'msisdn': '51916143575', 'nro_suministro': '916143575', 'registro': 'PEA716C18282482025-06-1213.29.15008218550000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('59.95')}, {'financial_id': Decimal('1162506121330465096448'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 30, 46), 'numero_operacion': '5695993291578', 'msisdn': '51906701258', 'nro_suministro': '10099811', 'registro': 'PEA716C18294052025-06-1213.30.47008318570000000001', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electro_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('1012.60')}, {'financial_id': Decimal('5902506121344420877765'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 44, 41), 'numero_operacion': '5695993291921', 'msisdn': '51925586257', 'nro_suministro': '923480624', 'registro': 'PEA716C18430062025-06-1213.44.42008418580000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('25.00')}, {'financial_id': Decimal('5902506121347014597949'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 47, 1), 'numero_operacion': '5695993292057', 'msisdn': '51968785328', 'nro_suministro': '15217328', 'registro': 'PEA716C18463622025-06-1213.47.02008518600000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('14.60')}, {'financial_id': Decimal('1162506121348513767869'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 48, 51), 'numero_operacion': '5695993292071', 'msisdn': '51910291130', 'nro_suministro': '910291130', 'registro': 'PEA716C18480802025-06-1213.48.52008618610000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('36.75')}, {'financial_id': Decimal('5902506121352086178346'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 52, 8), 'numero_operacion': '5695993292148', 'msisdn': '51938165768', 'nro_suministro': '8573780', 'registro': 'PEA716C18514502025-06-1213.52.09008718630000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('17.80')}, {'financial_id': Decimal('8502506121352590687541'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 52, 58), 'numero_operacion': '5695993292150', 'msisdn': '51946959744', 'nro_suministro': '72143197', 'registro': 'PEA716C18515202025-06-1213.52.59008818640000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EDPYME_ProEmpresa@WU', 'service_charge': Decimal('1.50'), 'total': Decimal('337.90')}, {'financial_id': Decimal('8502506121357416037881'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 57, 41), 'numero_operacion': '5695993292318', 'msisdn': '51975424368', 'nro_suministro': '975424368', 'registro': 'PEA716C18570782025-06-1213.57.42008918660000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('38.25')}, {'financial_id': Decimal('8502506121358292397948'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 58, 28), 'numero_operacion': '5695993292326', 'msisdn': '51994198481', 'nro_suministro': '994198481', 'registro': 'PEA716C18573632025-06-1213.58.30009018680000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('260.90')}, {'financial_id': Decimal('5902506121359008438839'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 59), 'numero_operacion': '5695993292333', 'msisdn': '51976461806', 'nro_suministro': '976461806', 'registro': 'PEA716C18581652025-06-1213.59.01009118700000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('1162506121359444808699'), 'payment_time': datetime.datetime(2025, 6, 12, 13, 59, 44), 'numero_operacion': '5695993292367', 'msisdn': '51985846713', 'nro_suministro': '985846713', 'registro': 'PEA716C18590782025-06-1213.59.45009218720000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('47.10')}, {'financial_id': Decimal('8502506121400434008129'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 0, 43), 'numero_operacion': '5695993292417', 'msisdn': '51941640606', 'nro_suministro': '72784952', 'registro': 'PEA716C19002272025-06-1214.00.44009318740000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('59.50')}, {'financial_id': Decimal('8502506121405552848567'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 5, 54), 'numero_operacion': '5695993292603', 'msisdn': '51910356417', 'nro_suministro': '976110300', 'registro': 'PEA716C19053912025-06-1214.05.56009418750000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('27.10')}, {'financial_id': Decimal('5902506121406438789469'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 6, 43), 'numero_operacion': '5695993292604', 'msisdn': '51984402186', 'nro_suministro': '984402186', 'registro': 'PEA716C19055152025-06-1214.06.44009518770000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('34.10')}, {'financial_id': Decimal('5902506121410108319705'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 10, 10), 'numero_operacion': '5695993292670', 'msisdn': '51936737964', 'nro_suministro': '053376940', 'registro': 'PEA716C19093572025-06-1214.10.11009618790000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('347.39')}, {'financial_id': Decimal('5902506121410307829722'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 10, 30), 'numero_operacion': '5695993292671', 'msisdn': '51935405667', 'nro_suministro': '935405667', 'registro': 'PEA716C19100452025-06-1214.10.31009718800000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.10')}, {'financial_id': Decimal('5902506121428304431172'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 28, 29), 'numero_operacion': '5695993293212', 'msisdn': '51989417941', 'nro_suministro': '17759039', 'registro': 'PEA716C19280712025-06-1214.28.31009818820000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('195.20')}, {'financial_id': Decimal('8502506121429000430380'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 28, 59), 'numero_operacion': '5695993293223', 'msisdn': '51991071116', 'nro_suministro': '053064698', 'registro': 'PEA716C19284022025-06-1214.29.00009918830000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('439.66')}, {'financial_id': Decimal('5902506121429570391283'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 29, 56), 'numero_operacion': '5695993293235', 'msisdn': '51956267079', 'nro_suministro': '18016896', 'registro': 'PEA716C19294292025-06-1214.29.57010018840000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('648.50')}, {'financial_id': Decimal('1162506121438319631837'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 38, 31), 'numero_operacion': '5695993293449', 'msisdn': '51922942553', 'nro_suministro': '13189903', 'registro': 'PEA716C19373512025-06-1214.38.32010118850000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('112.80')}, {'financial_id': Decimal('5902506121439169112010'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 39, 16), 'numero_operacion': '5695993293464', 'msisdn': '51943336990', 'nro_suministro': '001144457270', 'registro': 'PEA716C19383952025-06-1214.39.17010218860000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('16.00')}, {'financial_id': Decimal('1162506121442093732166'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 42, 8), 'numero_operacion': '5695993293556', 'msisdn': '51907062454', 'nro_suministro': '907062454 ', 'registro': 'PEA716C19414242025-06-1214.42.10010318880000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('61.10')}, {'financial_id': Decimal('5902506121443598682436'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 43, 59), 'numero_operacion': '5695993293559', 'msisdn': '51984725748', 'nro_suministro': '8572756', 'registro': 'PEA716C19423292025-06-1214.44.01010418900000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('111.10')}, {'financial_id': Decimal('1162506121444259622363'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 44, 25), 'numero_operacion': '5695993293624', 'msisdn': '51968785328', 'nro_suministro': '17610589', 'registro': 'PEA716C19441082025-06-1214.44.26010518910000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('19.00')}, {'financial_id': Decimal('8502506121444285841595'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 44, 28), 'numero_operacion': '0', 'msisdn': '51936206987', 'nro_suministro': '920728333', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('15.83')}, {'financial_id': Decimal('1162506121445367782459'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 45, 36), 'numero_operacion': '0', 'msisdn': '51936206987', 'nro_suministro': '920728333', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('15.83')}, {'financial_id': Decimal('1162506121454160683138'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 54, 15), 'numero_operacion': '5695993293874', 'msisdn': '51900873617', 'nro_suministro': '18005722', 'registro': 'PEA716C19540462025-06-1214.54.16010818920000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('8.80')}, {'financial_id': Decimal('1162506121454315383154'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 54, 31), 'numero_operacion': '5695993293873', 'msisdn': '51912644344', 'nro_suministro': '1176108', 'registro': 'PEA716C19533632025-06-1214.54.32010918930000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('226.40')}, {'financial_id': Decimal('1162506121458455263475'), 'payment_time': datetime.datetime(2025, 6, 12, 14, 58, 45), 'numero_operacion': '5695993293985', 'msisdn': '51933136114', 'nro_suministro': '933136114', 'registro': 'PEA716C19563532025-06-1214.58.46011018940000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('72.86')}, {'financial_id': Decimal('1162506121501103913692'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 1, 9), 'numero_operacion': '5695993294138', 'msisdn': '51980421542', 'nro_suministro': '10429184', 'registro': 'PEA716C20002482025-06-1215.01.11011118960000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EDPYME_ProEmpresa@WU', 'service_charge': Decimal('1.50'), 'total': Decimal('628.90')}, {'financial_id': Decimal('1162506121514332744709'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 14, 32), 'numero_operacion': '5695993294519', 'msisdn': '51998199502', 'nro_suministro': '998199502', 'registro': 'PEA716C20135882025-06-1215.14.33011218980000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('57.75')}, {'financial_id': Decimal('8502506121516372644061'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 16, 36), 'numero_operacion': '5695993294516', 'msisdn': '51929314469', 'nro_suministro': '12618047', 'registro': 'PEA716C20134642025-06-1215.16.38011319000000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('69.00')}, {'financial_id': Decimal('1162506121516374914840'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 16, 36), 'numero_operacion': '5695993294562', 'msisdn': '51969727599', 'nro_suministro': '47691926', 'registro': 'PEA716C20160662025-06-1215.16.38011419010000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('38.60')}, {'financial_id': Decimal('5902506121522288095457'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 22, 28), 'numero_operacion': '5695993294739', 'msisdn': '51955485992', 'nro_suministro': '16376552', 'registro': 'PEA716C20221612025-06-1215.22.29011519020000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('59.70')}, {'financial_id': Decimal('1162506121522345935315'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 22, 34), 'numero_operacion': '5695993294715', 'msisdn': '51973912685', 'nro_suministro': '1595728', 'registro': 'PEA716C20215762025-06-1215.22.35011619030000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('347.80')}, {'financial_id': Decimal('1162506121523045355357'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 23, 4), 'numero_operacion': '5695993294744', 'msisdn': '51931538578', 'nro_suministro': '72229550', 'registro': 'PEA716C20225052025-06-1215.23.05011719040000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('318.40')}, {'financial_id': Decimal('8502506121523228994612'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 23, 22), 'numero_operacion': '5695993294748', 'msisdn': '51950931421', 'nro_suministro': '914216201', 'registro': 'PEA716C20225942025-06-1215.23.23011819050000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('74.00')}, {'financial_id': Decimal('5902506121523576255580'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 23, 57), 'numero_operacion': '5695993294752', 'msisdn': '51931538578', 'nro_suministro': '76867033', 'registro': 'PEA716C20234432025-06-1215.23.58011919070000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('92.40')}, {'financial_id': Decimal('5902506121526455125785'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 26, 45), 'numero_operacion': '5695993294840', 'msisdn': '51907820392', 'nro_suministro': '907820392', 'registro': 'PEA716C20262512025-06-1215.26.46012019080000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('108.06')}, {'financial_id': Decimal('1162506121533381086199'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 33, 37), 'numero_operacion': '5695993295024', 'msisdn': '51977168293', 'nro_suministro': '16797302', 'registro': 'PEA716C20330292025-06-1215.33.38012119100000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('15.50')}, {'financial_id': Decimal('8502506121536076095623'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 36, 7), 'numero_operacion': '5695993295107', 'msisdn': '51931538578', 'nro_suministro': '72212179', 'registro': 'PEA716C20355112025-06-1215.36.08012219110000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('24.30')}, {'financial_id': Decimal('1162506121547396947377'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 47, 39), 'numero_operacion': '5695993295448', 'msisdn': '51946951529', 'nro_suministro': '053385699', 'registro': 'PEA716C20472492025-06-1215.47.40012319120000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.00')}, {'financial_id': Decimal('5902506121547522467546'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 47, 51), 'numero_operacion': '5695993295452', 'msisdn': '51984725748', 'nro_suministro': '8572550', 'registro': 'PEA716C20472962025-06-1215.47.52012419130000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('40.60')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 56, 36), 'numero_operacion': '0', 'msisdn': '51958006326', 'nro_suministro': '958006326 ', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('8502506121556582067369'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 56, 57), 'numero_operacion': '5695993295726', 'msisdn': '51958006326', 'nro_suministro': '958006326 ', 'registro': 'PEA716C20564612025-06-1215.56.59012519140000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('8502506121557322657418'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 57, 31), 'numero_operacion': '5695993295731', 'msisdn': '51976855925', 'nro_suministro': '36715814', 'registro': 'PEA716C20571992025-06-1215.57.33012619160000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('66.10')}, {'financial_id': Decimal('5902506121559340898545'), 'payment_time': datetime.datetime(2025, 6, 12, 15, 59, 33), 'numero_operacion': '5695993295800', 'msisdn': '51936270581', 'nro_suministro': '936270581', 'registro': 'PEA716C20590392025-06-1215.59.34012719170000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('46.10')}, {'financial_id': Decimal('8502506121601456047767'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 1, 45), 'numero_operacion': '5695993295902', 'msisdn': '51906302116', 'nro_suministro': '10577375', 'registro': 'PEA716C21011522025-06-1216.01.46012819190000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('93.00')}, {'financial_id': Decimal('8502506121611137898592'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 11, 13), 'numero_operacion': '5695993296152', 'msisdn': '51955449489', 'nro_suministro': '49365553', 'registro': 'PEA716C21105472025-06-1216.11.14012919200000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.30')}, {'financial_id': Decimal('5902506121611429429614'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 11, 42), 'numero_operacion': '5695993296179', 'msisdn': '51917241345', 'nro_suministro': '917241345', 'registro': 'PEA716C21112692025-06-1216.11.43013019210000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('255.20')}, {'financial_id': Decimal('8502506121611466788647'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 11, 46), 'numero_operacion': '5695993296180', 'msisdn': '51955449489', 'nro_suministro': '49365553', 'registro': 'PEA716C21113412025-06-1216.11.47013119230000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.30')}, {'financial_id': Decimal('1162506121612068989483'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 12, 6), 'numero_operacion': '5695993296178', 'msisdn': '51954805973', 'nro_suministro': '945114472', 'registro': 'PEA716C21112272025-06-1216.12.07013219240000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('8502506121612288608709'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 12, 28), 'numero_operacion': '5695993296184', 'msisdn': '51969887758', 'nro_suministro': '10785841', 'registro': 'PEA716C21121072025-06-1216.12.29013319260000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('48.80')}, {'financial_id': Decimal('1162506121614091169663'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 14, 8), 'numero_operacion': '5695993296245', 'msisdn': '51936241931', 'nro_suministro': '936241931', 'registro': 'PEA716C21135272025-06-1216.14.09013419270000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('55.93')}, {'financial_id': Decimal('8502506121616040519038'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 16, 3), 'numero_operacion': '5695993296277', 'msisdn': '51936241931', 'nro_suministro': '981460353', 'registro': 'PEA716C21154312025-06-1216.16.04013519290000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('44.96')}, {'financial_id': Decimal('1162506121623034580438'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 23, 3), 'numero_operacion': '5695993296443', 'msisdn': '51917418003', 'nro_suministro': '7125519', 'registro': 'PEA716C21224562025-06-1216.23.04013619310000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('79.40')}, {'financial_id': Decimal('5902506121627553731013'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 27, 55), 'numero_operacion': '5695993296555', 'msisdn': '51948439595', 'nro_suministro': '920473229', 'registro': 'PEA716C21271892025-06-1216.27.56013719330000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('37.10')}, {'financial_id': Decimal('1162506121632359601277'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 32, 35), 'numero_operacion': '5695993296705', 'msisdn': '51925852739', 'nro_suministro': '925852739', 'registro': 'PEA716C21320952025-06-1216.32.36013819350000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 32, 52), 'numero_operacion': '0', 'msisdn': '51925135436', 'nro_suministro': '47016079', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('16.00')}, {'financial_id': Decimal('1162506121633215051346'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 33, 21), 'numero_operacion': '5695993296710', 'msisdn': '51926891124', 'nro_suministro': '053189970', 'registro': 'PEA716C21330082025-06-1216.33.22013919370000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('251.18')}, {'financial_id': Decimal('5902506121635401361701'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 35, 39), 'numero_operacion': '5695993296766', 'msisdn': '51930537097', 'nro_suministro': '053677320', 'registro': 'PEA716C21344762025-06-1216.35.40014019380000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('214.37')}, {'financial_id': Decimal('5902506121638598422014'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 38, 59), 'numero_operacion': '5695993296881', 'msisdn': '51926891124', 'nro_suministro': '0747321', 'registro': 'PEA716C21384822025-06-1216.39.00014119390000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('450.50')}, {'financial_id': Decimal('1162506121640492282012'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 40, 48), 'numero_operacion': '5695993296938', 'msisdn': '51929192555', 'nro_suministro': '2706509', 'registro': 'PEA716C21400942025-06-1216.40.49014219400000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('87.50')}, {'financial_id': Decimal('1162506121642327192169'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 42, 32), 'numero_operacion': '5695993296952', 'msisdn': '51955485992', 'nro_suministro': '17931979', 'registro': 'PEA716C21421932025-06-1216.42.33014319410000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('6.70')}, {'financial_id': Decimal('1162506121643082512232'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 43, 7), 'numero_operacion': '5695993297001', 'msisdn': '51922090983', 'nro_suministro': '923663423', 'registro': 'PEA716C21423622025-06-1216.43.08014419420000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('39.95')}, {'financial_id': Decimal('1162506121643223982251'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 43, 22), 'numero_operacion': '5695993297003', 'msisdn': '51971404264', 'nro_suministro': '0492963', 'registro': 'PEA716C21425812025-06-1216.43.23014519440000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('894.00')}, {'financial_id': Decimal('1162506121643505242289'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 43, 50), 'numero_operacion': '5695993297005', 'msisdn': '51901354425', 'nro_suministro': '901354425', 'registro': 'PEA716C21431222025-06-1216.43.51014619450000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('8502506121644377471572'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 44, 37), 'numero_operacion': '5695993297014', 'msisdn': '51929192555', 'nro_suministro': '3158802', 'registro': 'PEA716C21441992025-06-1216.44.38014719470000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('69.00')}, {'financial_id': Decimal('8502506121645558041675'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 45, 55), 'numero_operacion': '5695993297102', 'msisdn': '51955485992', 'nro_suministro': '17931979', 'registro': 'PEA716C21452692025-06-1216.45.56014819480000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('14.90')}, {'financial_id': Decimal('8502506121648395051879'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 48, 39), 'numero_operacion': '5695993297166', 'msisdn': '51955485992', 'nro_suministro': '17931979', 'registro': 'PEA716C21475572025-06-1216.48.40014919490000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('6.80')}, {'financial_id': Decimal('5902506121649362212941'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 49, 35), 'numero_operacion': '5695993297221', 'msisdn': '51955485992', 'nro_suministro': '17931979', 'registro': 'PEA716C21490092025-06-1216.49.36015019500000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('6.80')}, {'financial_id': Decimal('5902506121650180323005'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 50, 17), 'numero_operacion': '5695993297257', 'msisdn': '51955485992', 'nro_suministro': '17931979', 'registro': 'PEA716C21495642025-06-1216.50.18015119510000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('14.80')}, {'financial_id': Decimal('1162506121651181692926'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 51, 17), 'numero_operacion': '5695993297269', 'msisdn': '51955485992', 'nro_suministro': '17931979', 'registro': 'PEA716C21503952025-06-1216.51.18015219520000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('15.00')}, {'financial_id': Decimal('1162506121652097503008'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 52, 9), 'numero_operacion': '5695993297298', 'msisdn': '51955485992', 'nro_suministro': '17931979', 'registro': 'PEA716C21514742025-06-1216.52.10015319530000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('16.00')}, {'financial_id': Decimal('5902506121653150053275'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 53, 14), 'numero_operacion': '5695993297304', 'msisdn': '51955485992', 'nro_suministro': '17931979', 'registro': 'PEA716C21524562025-06-1216.53.15015419540000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.10')}, {'financial_id': Decimal('1162506121654213083186'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 54, 20), 'numero_operacion': '5695993297307', 'msisdn': '51908755045', 'nro_suministro': '908755045', 'registro': 'PEA716C21531842025-06-1216.54.21015519550000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('23.26')}, {'financial_id': Decimal('8502506121654258922432'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 54, 25), 'numero_operacion': '5695993297315', 'msisdn': '51939480677', 'nro_suministro': '76218871', 'registro': 'PEA716C21541012025-06-1216.54.26015619570000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.00')}, {'financial_id': Decimal('8502506121655327722526'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 55, 32), 'numero_operacion': '5695993297362', 'msisdn': '51939480677', 'nro_suministro': '70271835', 'registro': 'PEA716C21544542025-06-1216.55.33015719580000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.00')}, {'financial_id': Decimal('5902506121656018273537'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 56, 1), 'numero_operacion': '5695993297357', 'msisdn': '51985526214', 'nro_suministro': '47812683', 'registro': 'PEA716C21541562025-06-1216.56.02015819590000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('45.10')}, {'financial_id': Decimal('8502506121656205472595'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 56, 20), 'numero_operacion': '5695993297368', 'msisdn': '51939480677', 'nro_suministro': '22078248', 'registro': 'PEA716C21560132025-06-1216.56.21015919600000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.00')}, {'financial_id': Decimal('8502506121657021052669'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 57, 1), 'numero_operacion': '5695993297372', 'msisdn': '51939480677', 'nro_suministro': '70560749', 'registro': 'PEA716C21564692025-06-1216.57.02016019610000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.00')}, {'financial_id': Decimal('8502506121657411902733'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 57, 40), 'numero_operacion': '5695993297457', 'msisdn': '51939480677', 'nro_suministro': '22067565', 'registro': 'PEA716C21571842025-06-1216.57.42016119620000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.00')}, {'financial_id': Decimal('1162506121658241283561'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 58, 23), 'numero_operacion': '5695993297465', 'msisdn': '51939480677', 'nro_suministro': '22062889', 'registro': 'PEA716C21580202025-06-1216.58.24016219630000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.00')}, {'financial_id': Decimal('5902506121658535993806'), 'payment_time': datetime.datetime(2025, 6, 12, 16, 58, 53), 'numero_operacion': '5695993297467', 'msisdn': '51939480677', 'nro_suministro': '70271834', 'registro': 'PEA716C21583962025-06-1216.58.54016319640000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.00')}, {'financial_id': Decimal('1162506121700179203742'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 0, 17), 'numero_operacion': '5695993297471', 'msisdn': '51917842696', 'nro_suministro': '17984829', 'registro': 'PEA716C21591522025-06-1217.00.18016419650000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('42.60')}, {'financial_id': Decimal('1162506121706070654265'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 6, 6), 'numero_operacion': '5695993297633', 'msisdn': '51941936085', 'nro_suministro': '941936085', 'registro': 'PEA716C22054082025-06-1217.06.07016519660000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('70.60')}, {'financial_id': Decimal('8502506121706105973481'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 6, 10), 'numero_operacion': '5695993297630', 'msisdn': '51943885661', 'nro_suministro': '943885661', 'registro': 'PEA716C22053762025-06-1217.06.11016619680000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('7.60')}, {'financial_id': Decimal('5902506121712216675019'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 12, 21), 'numero_operacion': '5695993297859', 'msisdn': '51974416558', 'nro_suministro': '48871553', 'registro': 'PEA716C22115682025-06-1217.12.22016719700000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('40.70')}, {'financial_id': Decimal('5902506121712445635058'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 12, 44), 'numero_operacion': '5695993297862', 'msisdn': '51910601787', 'nro_suministro': '910601787', 'registro': 'PEA716C22121412025-06-1217.12.45016819710000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('50.47')}, {'financial_id': Decimal('5902506121716004155356'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 16), 'numero_operacion': '5695993297947', 'msisdn': '51938941183', 'nro_suministro': '938941183', 'registro': 'PEA716C22152512025-06-1217.16.01016919730000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('8502506121717329864533'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 17, 31), 'numero_operacion': '5695993298008', 'msisdn': '51943885661', 'nro_suministro': '943885661', 'registro': 'PEA716C22170342025-06-1217.17.33017019750000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('8.35')}, {'financial_id': Decimal('5902506121718384765588'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 18, 38), 'numero_operacion': '5695993298032', 'msisdn': '51943885661', 'nro_suministro': '943885661', 'registro': 'PEA716C22180962025-06-1217.18.39017119770000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('8.70')}, {'financial_id': Decimal('8502506121719591544733'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 19, 58), 'numero_operacion': '5695993298082', 'msisdn': '51928752350', 'nro_suministro': '910730941', 'registro': 'PEA716C22193722025-06-1217.19.59017219790000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('5.10')}, {'financial_id': Decimal('5902506121729314166655'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 29, 30), 'numero_operacion': '5695993298299', 'msisdn': '51983576286', 'nro_suministro': '049450419', 'registro': 'PEA716C22281602025-06-1217.29.32017319810000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('264.78')}, {'financial_id': Decimal('1162506121731060456666'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 31, 5), 'numero_operacion': '5695993298343', 'msisdn': '51940875297', 'nro_suministro': '011192645', 'registro': 'PEA716C22301732025-06-1217.31.06017419820000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('271.31')}, {'financial_id': Decimal('5902506121734008267077'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 34), 'numero_operacion': '5695993298414', 'msisdn': '51951474141', 'nro_suministro': '10019807', 'registro': 'PEA716C22333242025-06-1217.34.01017519830000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EMSA_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('38.10')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 34, 51), 'numero_operacion': '0', 'msisdn': '51953110761', 'nro_suministro': '052715334', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('459.43')}, {'financial_id': Decimal('1162506121736340087162'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 36, 33), 'numero_operacion': '5695993298533', 'msisdn': '51902383858', 'nro_suministro': '902383858', 'registro': 'PEA716C22361942025-06-1217.36.34017619840000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('36.20')}, {'financial_id': Decimal('5902506121739347187632'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 39, 34), 'numero_operacion': '5695993298616', 'msisdn': '51953110761', 'nro_suministro': '052715334', 'registro': 'PEA716C22391202025-06-1217.39.35017719860000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('459.43')}, {'financial_id': Decimal('5902506121741505677838'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 41, 50), 'numero_operacion': '5695993298678', 'msisdn': '51940631468', 'nro_suministro': '11289454', 'registro': 'PEA716C22411092025-06-1217.41.51017819870000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('66.20')}, {'financial_id': Decimal('5902506121742036657851'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 42, 3), 'numero_operacion': '5695993298677', 'msisdn': '51952936219', 'nro_suministro': '46587783', 'registro': 'PEA716C22410832025-06-1217.42.04017919880000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('536.10')}, {'financial_id': Decimal('5902506121743250147975'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 43, 24), 'numero_operacion': '5695993298693', 'msisdn': '51972120679', 'nro_suministro': '972120679', 'registro': 'PEA716C22425732025-06-1217.43.25018019890000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('57.10')}, {'financial_id': Decimal('1162506121746408488074'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 46, 40), 'numero_operacion': '5695993298819', 'msisdn': '51969887758', 'nro_suministro': '10785850', 'registro': 'PEA716C22462872025-06-1217.46.41018119910000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.60')}, {'financial_id': Decimal('8502506121750556667648'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 50, 55), 'numero_operacion': '5695993298907', 'msisdn': '51984760979', 'nro_suministro': '958558831', 'registro': 'PEA716C22502802025-06-1217.50.56018219920000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('42.40')}, {'financial_id': Decimal('5902506121753000198837'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 52, 59), 'numero_operacion': '5695993298974', 'msisdn': '51957160638', 'nro_suministro': '10803841', 'registro': 'PEA716C22523262025-06-1217.53.00018319940000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('49.70')}, {'financial_id': Decimal('5902506121753407528910'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 53, 40), 'numero_operacion': '5695993299005', 'msisdn': '51957160638', 'nro_suministro': '10803841', 'registro': 'PEA716C22532732025-06-1217.53.41018419950000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('42.00')}, {'financial_id': Decimal('1162506121754538018842'), 'payment_time': datetime.datetime(2025, 6, 12, 17, 54, 53), 'numero_operacion': '5695993299041', 'msisdn': '51957160638', 'nro_suministro': '18520468', 'registro': 'PEA716C22544202025-06-1217.54.54018519960000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('43.50')}, {'financial_id': Decimal('1162506121801041619436'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 1, 3), 'numero_operacion': '5695993299178', 'msisdn': '51912412417', 'nro_suministro': '912412417', 'registro': 'PEA716C23000332025-06-1218.01.04018619970000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('110.50')}, {'financial_id': Decimal('8502506121801077798657'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 1, 7), 'numero_operacion': '5695993299176', 'msisdn': '51921032352', 'nro_suministro': '984042376', 'registro': 'PEA716C22595452025-06-1218.01.08018719990000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('192.20')}, {'financial_id': Decimal('8502506121802493538794'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 2, 48), 'numero_operacion': '5695993299244', 'msisdn': '51940143683', 'nro_suministro': '46542529', 'registro': 'PEA716C23022082025-06-1218.02.50018820010000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('831.80')}, {'financial_id': Decimal('1162506121806193550027'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 6, 18), 'numero_operacion': '5695993299329', 'msisdn': '51957160638', 'nro_suministro': '10917397', 'registro': 'PEA716C23060612025-06-1218.06.20018920020000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('6.40')}, {'financial_id': Decimal('5902506121807007830313'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 7), 'numero_operacion': '5695993299335', 'msisdn': '51957160638', 'nro_suministro': '10917397', 'registro': 'PEA716C23064872025-06-1218.07.01019020030000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.60')}, {'financial_id': Decimal('5902506121808218460511'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 8, 21), 'numero_operacion': '5695993299362', 'msisdn': '51969887758', 'nro_suministro': '10880457', 'registro': 'PEA716C23075872025-06-1218.08.22019120040000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('108.90')}, {'financial_id': Decimal('8502506121808572669682'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 8, 56), 'numero_operacion': '5695993299372', 'msisdn': '51945331604', 'nro_suministro': '18228835', 'registro': 'PEA716C23084302025-06-1218.08.58019220050000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('68.70')}, {'financial_id': Decimal('1162506121811587881476'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 11, 58), 'numero_operacion': '5695993299442', 'msisdn': '51969887758', 'nro_suministro': '10996022', 'registro': 'PEA716C23113982025-06-1218.11.59019320060000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('84.10')}, {'financial_id': Decimal('5902506121812520651838'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 12, 51), 'numero_operacion': '5695993299476', 'msisdn': '51923071134', 'nro_suministro': '70220313', 'registro': 'PEA716C23124042025-06-1218.12.52019420070000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('16.40')}, {'financial_id': Decimal('8502506121816452911698'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 16, 44), 'numero_operacion': '5695993299569', 'msisdn': '51955351162', 'nro_suministro': '17381753', 'registro': 'PEA716C23163362025-06-1218.16.46019520080000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('16.00')}, {'financial_id': Decimal('5902506121819501873042'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 19, 49), 'numero_operacion': '5695993299641', 'msisdn': '51912412417', 'nro_suministro': '912412417', 'registro': 'PEA716C23190822025-06-1218.19.50019620090000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('110.50')}, {'financial_id': Decimal('5902506121820077893075'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 20, 7), 'numero_operacion': '5695993299645', 'msisdn': '51967464517', 'nro_suministro': '78134768', 'registro': 'PEA716C23194222025-06-1218.20.08019720110000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.00')}, {'financial_id': Decimal('8502506121821059122312'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 21, 5), 'numero_operacion': '5695993299650', 'msisdn': '51955351162', 'nro_suministro': '11046429', 'registro': 'PEA716C23202962025-06-1218.21.06019820120000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('45.80')}, {'financial_id': Decimal('8502506121821122802329'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 21, 11), 'numero_operacion': '5695993299654', 'msisdn': '51901213904', 'nro_suministro': '053662048', 'registro': 'PEA716C23204812025-06-1218.21.13019920130000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('300.95')}, {'financial_id': Decimal('1162506121821148033194'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 21, 14), 'numero_operacion': '5695993299655', 'msisdn': '51931342524', 'nro_suministro': '001445038236', 'registro': 'PEA716C23205202025-06-1218.21.15020020140000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('16.00')}, {'financial_id': Decimal('5902506121830372284414'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 30, 36), 'numero_operacion': '5695993299917', 'msisdn': '51927231853', 'nro_suministro': '923160002', 'registro': 'PEA716C23294692025-06-1218.30.37020120160000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('925.70')}, {'financial_id': Decimal('5902506121832119384570'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 32, 11), 'numero_operacion': '5695993299963', 'msisdn': '51912412417', 'nro_suministro': '979432068', 'registro': 'PEA716C23312972025-06-1218.32.12020220180000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('7.60')}, {'financial_id': Decimal('8502506121833255133730'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 33, 25), 'numero_operacion': '5695993300003', 'msisdn': '51900834009', 'nro_suministro': '83288380', 'registro': 'PEA716C23331012025-06-1218.33.26020320200000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.50')}, {'financial_id': Decimal('5902506121833569984745'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 33, 56), 'numero_operacion': '5695993300007', 'msisdn': '51966955790', 'nro_suministro': '043676911', 'registro': 'PEA716C23334192025-06-1218.33.57020420210000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('61.00')}, {'financial_id': Decimal('8502506121835130583907'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 35, 12), 'numero_operacion': '5695993300012', 'msisdn': '51969887758', 'nro_suministro': '10783050', 'registro': 'PEA716C23340252025-06-1218.35.13020520220000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('95.30')}, {'financial_id': Decimal('5902506121835179234860'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 35, 17), 'numero_operacion': '5695993300010', 'msisdn': '51912727204', 'nro_suministro': '528994', 'registro': 'PEA716C23335292025-06-1218.35.18020620230000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('57.00')}, {'financial_id': Decimal('5902506121837162405036'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 37, 15), 'numero_operacion': '5695993300093', 'msisdn': '51975456922', 'nro_suministro': '965968590', 'registro': 'PEA716C23363702025-06-1218.37.16020720240000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('51.10')}, {'financial_id': Decimal('1162506121838022555008'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 38, 1), 'numero_operacion': '5695993300127', 'msisdn': '51984549898', 'nro_suministro': '984549898', 'registro': 'PEA716C23373432025-06-1218.38.02020820260000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('50.00')}, {'financial_id': Decimal('1162506121838156515034'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 38, 15), 'numero_operacion': '5695993300125', 'msisdn': '51953896863', 'nro_suministro': '949612746', 'registro': 'PEA716C23371342025-06-1218.38.16020920280000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('65.90')}, {'financial_id': Decimal('8502506121839058724258'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 39, 5), 'numero_operacion': '5695993300148', 'msisdn': '51984549898', 'nro_suministro': '984552474', 'registro': 'PEA716C23384342025-06-1218.39.06021020300000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('50.00')}, {'financial_id': Decimal('1162506121841283585373'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 41, 27), 'numero_operacion': '5695993300221', 'msisdn': '51984549898', 'nro_suministro': '1202855', 'registro': 'PEA716C23410372025-06-1218.41.29021120320000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('275.50')}, {'financial_id': Decimal('8502506121843122074623'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 43, 11), 'numero_operacion': '5695993300241', 'msisdn': '51919589872', 'nro_suministro': '10451331', 'registro': 'PEA716C23423562025-06-1218.43.13021220330000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('337.40')}, {'financial_id': Decimal('5902506121843280215585'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 43, 27), 'numero_operacion': '5695993300238', 'msisdn': '51974574958', 'nro_suministro': '001126304615', 'registro': 'PEA716C23421942025-06-1218.43.28021320340000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('16.00')}, {'financial_id': Decimal('5902506121844282145684'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 44, 27), 'numero_operacion': '5695993300253', 'msisdn': '51969887758', 'nro_suministro': '17962026', 'registro': 'PEA716C23440952025-06-1218.44.28021420360000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('41.50')}, {'financial_id': Decimal('5902506121844412315697'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 44, 40), 'numero_operacion': '5695993300297', 'msisdn': '51936845843', 'nro_suministro': '936845843', 'registro': 'PEA716C23441792025-06-1218.44.41021520370000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('44.37')}, {'financial_id': Decimal('8502506121852455365741'), 'payment_time': datetime.datetime(2025, 6, 12, 18, 52, 44), 'numero_operacion': '5695993300415', 'msisdn': '51942997251', 'nro_suministro': '46542725', 'registro': 'PEA716C23521402025-06-1218.52.46021620390000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('228.10')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 5, 48), 'numero_operacion': '0', 'msisdn': '51931839097', 'nro_suministro': '926512863', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('39.90')}, {'financial_id': Decimal('8502506121906557886918'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 6, 55), 'numero_operacion': '5695993300618', 'msisdn': '51976151393', 'nro_suministro': '976151393', 'registro': 'PEA716C00062592025-06-1219.06.56021720400000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('261.20')}, {'financial_id': Decimal('1162506121907131567752'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 7, 12), 'numero_operacion': '5695993300626', 'msisdn': '51980931775', 'nro_suministro': '183589', 'registro': 'PEA716C00065422025-06-1219.07.13021820420000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('588.40')}, {'financial_id': Decimal('5902506121907157307787'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 7, 15), 'numero_operacion': '5695993300620', 'msisdn': '51931839097', 'nro_suministro': '926512863', 'registro': 'PEA716C00063112025-06-1219.07.16021920430000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('39.90')}, {'financial_id': Decimal('8502506121908154547037'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 8, 15), 'numero_operacion': '5695993300656', 'msisdn': '51994259756', 'nro_suministro': '053232191', 'registro': 'PEA716C00080282025-06-1219.08.16022020450000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('520.46')}, {'financial_id': Decimal('5902506121909031807921'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 9, 2), 'numero_operacion': '5695993300661', 'msisdn': '51917217872', 'nro_suministro': '917217872', 'registro': 'PEA716C00082692025-06-1219.09.03022120460000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('127.80')}, {'financial_id': Decimal('1162506121910197427993'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 10, 19), 'numero_operacion': '5695993300674', 'msisdn': '51951194359', 'nro_suministro': '341908', 'registro': 'PEA716C00100362025-06-1219.10.20022220480000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('386.00')}, {'financial_id': Decimal('5902506121910247108025'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 10, 24), 'numero_operacion': '5695993300675', 'msisdn': '51969887758', 'nro_suministro': '10850055', 'registro': 'PEA716C00100552025-06-1219.10.25022320490000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('13.50')}, {'financial_id': Decimal('1162506121923581159052'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 23, 57), 'numero_operacion': '5695993300844', 'msisdn': '51982819571', 'nro_suministro': '25665080', 'registro': 'PEA716C00231862025-06-1219.23.58022420500000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('157.50')}, {'financial_id': Decimal('8502506121925219498350'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 25, 21), 'numero_operacion': '5695993300878', 'msisdn': '51974416558', 'nro_suministro': '48901320', 'registro': 'PEA716C00245832025-06-1219.25.22022520510000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('87.60')}, {'financial_id': Decimal('8502506121928253928586'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 28, 24), 'numero_operacion': '5695993300904', 'msisdn': '51951966702', 'nro_suministro': '951966702', 'registro': 'PEA716C00280472025-06-1219.28.26022620520000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('47.20')}, {'financial_id': Decimal('5902506121934411589978'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 34, 40), 'numero_operacion': '5695993300985', 'msisdn': '51983733585', 'nro_suministro': '988184911', 'registro': 'PEA716C00340092025-06-1219.34.41022720540000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('15.30')}, {'financial_id': Decimal('1162506121941122080331'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 41, 11), 'numero_operacion': '5695993301069', 'msisdn': '51921849735', 'nro_suministro': '921849735', 'registro': 'PEA716C00402362025-06-1219.41.12022820560000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('36.15')}, {'financial_id': Decimal('1162506121942483740486'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 42, 47), 'numero_operacion': '5695993301084', 'msisdn': '51934122658', 'nro_suministro': '048162738', 'registro': 'PEA716C00414662025-06-1219.42.49022920580000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('435.00')}, {'financial_id': Decimal('5902506121945360730793'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 45, 35), 'numero_operacion': '0', 'msisdn': '51984725748', 'nro_suministro': '15816557', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.60')}, {'financial_id': Decimal('1162506121946578290797'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 46, 57), 'numero_operacion': '5695993301143', 'msisdn': '51984725748', 'nro_suministro': '15816557', 'registro': 'PEA716C00462472025-06-1219.46.58023120600000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('15.40')}, {'financial_id': Decimal('5902506121947089910915'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 47, 8), 'numero_operacion': '5695993301148', 'msisdn': '51910296014', 'nro_suministro': '958033608', 'registro': 'PEA716C00465652025-06-1219.47.09023220610000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('182.72')}, {'financial_id': Decimal('8502506121956517840739'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 56, 51), 'numero_operacion': '5695993301211', 'msisdn': '51940752390', 'nro_suministro': '053470831', 'registro': 'PEA716C00554012025-06-1219.56.52023320630000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('437.07')}, {'financial_id': Decimal('1162506121957567971568'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 57, 56), 'numero_operacion': '5695993301256', 'msisdn': '51968798902', 'nro_suministro': '968798902', 'registro': 'PEA716C00572552025-06-1219.57.57023420640000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('10.30')}, {'financial_id': Decimal('8502506121959082320907'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 59, 7), 'numero_operacion': '5695993301269', 'msisdn': '51956423444', 'nro_suministro': '956423444', 'registro': 'PEA716C00583552025-06-1219.59.08023520660000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('39.53')}, {'financial_id': Decimal('1162506121959400491675'), 'payment_time': datetime.datetime(2025, 6, 12, 19, 59, 39), 'numero_operacion': '5695993301273', 'msisdn': '51945848438', 'nro_suministro': '49366081', 'registro': 'PEA716C00590892025-06-1219.59.40023620680000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('136.50')}, {'financial_id': Decimal('5902506122001535241933'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 1, 53), 'numero_operacion': '5695993301339', 'msisdn': '51941412915', 'nro_suministro': '927060337', 'registro': 'PEA716C01010352025-06-1220.01.54023720700000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('49.89')}, {'financial_id': Decimal('1162506122007025832135'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 7, 2), 'numero_operacion': '5695993301447', 'msisdn': '51916589377', 'nro_suministro': '881330', 'registro': 'PEA716C01061782025-06-1220.07.03023820720000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('260.80')}, {'financial_id': Decimal('5902506122012453692591'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 12, 44), 'numero_operacion': '5695993301586', 'msisdn': '51902583606', 'nro_suministro': '986094870', 'registro': 'PEA716C01122112025-06-1220.12.46023920730000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506122014269432577'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 14, 26), 'numero_operacion': '5695993301607', 'msisdn': '51951933341', 'nro_suministro': '998996399', 'registro': 'PEA716C01140072025-06-1220.14.27024020750000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('1162506122025104293263'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 25, 9), 'numero_operacion': '5695993301795', 'msisdn': '51977587271', 'nro_suministro': '1087278', 'registro': 'PEA716C01242022025-06-1220.25.11024120770000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('44.60')}, {'financial_id': Decimal('8502506122030087192832'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 30, 8), 'numero_operacion': '5695993301938', 'msisdn': '51966464573', 'nro_suministro': '748771653', 'registro': 'PEA716C01292932025-06-1220.30.09024220780000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('111.10')}, {'financial_id': Decimal('1162506122030285023539'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 30, 28), 'numero_operacion': '5695993301937', 'msisdn': '51984013494', 'nro_suministro': '052544742', 'registro': 'PEA716C01292092025-06-1220.30.29024320800000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('46.04')}, {'financial_id': Decimal('1162506122033299583727'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 33, 29), 'numero_operacion': '5695993301992', 'msisdn': '51968743509', 'nro_suministro': '934805528', 'registro': 'PEA716C01323732025-06-1220.33.30024420810000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('114.00')}, {'financial_id': Decimal('5902506122034045153963'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 34, 4), 'numero_operacion': '5695993302021', 'msisdn': '51931275711', 'nro_suministro': '931275711', 'registro': 'PEA716C01332592025-06-1220.34.08024520830000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('5902506122035143454022'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 35, 14), 'numero_operacion': '5695993302057', 'msisdn': '51946835453', 'nro_suministro': '924640770', 'registro': 'PEA716C01345532025-06-1220.35.15024620850000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 37, 39), 'numero_operacion': '0', 'msisdn': '51952550728', 'nro_suministro': '968571526', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('40.00')}, {'financial_id': Decimal('1162506122039328184101'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 39, 32), 'numero_operacion': '5695993302114', 'msisdn': '51902481759', 'nro_suministro': '053143482', 'registro': 'PEA716C01390212025-06-1220.39.33024720870000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('350.00')}, {'financial_id': Decimal('8502506122040084323418'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 40, 8), 'numero_operacion': '5695993302146', 'msisdn': '51952550728', 'nro_suministro': '968571526', 'registro': 'PEA716C01395192025-06-1220.40.09024820880000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('40.00')}, {'financial_id': Decimal('8502506122041250293511'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 41, 24), 'numero_operacion': '5695993302176', 'msisdn': '51918287125', 'nro_suministro': '052603250', 'registro': 'PEA716C01410912025-06-1220.41.25024920900000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.00')}, {'financial_id': Decimal('8502506122043516173669'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 43, 51), 'numero_operacion': '5695993302206', 'msisdn': '51974193025', 'nro_suministro': '974193025 ', 'registro': 'PEA716C01431892025-06-1220.43.52025020910000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('68.00')}, {'financial_id': Decimal('1162506122046192364542'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 46, 18), 'numero_operacion': '5695993302236', 'msisdn': '51916343860', 'nro_suministro': '1480281', 'registro': 'PEA716C01443972025-06-1220.46.19025120930000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('32.30')}, {'financial_id': Decimal('8502506122050472554050'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 50, 46), 'numero_operacion': '5695993302324', 'msisdn': '51997020416', 'nro_suministro': '2840231', 'registro': 'PEA716C01495152025-06-1220.50.48025220940000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('42.50')}, {'financial_id': Decimal('1162506122050594224811'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 50, 59), 'numero_operacion': '5695993302336', 'msisdn': '51973912685', 'nro_suministro': '973912685', 'registro': 'PEA716C01502932025-06-1220.51.00025320950000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.30')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 12, 20, 55, 26), 'numero_operacion': '0', 'msisdn': '51963376781', 'nro_suministro': '963376781', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('39.70')}, {'financial_id': Decimal('5902506122107323185952'), 'payment_time': datetime.datetime(2025, 6, 12, 21, 7, 31), 'numero_operacion': '5695993302643', 'msisdn': '51961014643', 'nro_suministro': '961014643', 'registro': 'PEA716C02070062025-06-1221.07.32025420970000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('123.18')}, {'financial_id': Decimal('5902506122108233605997'), 'payment_time': datetime.datetime(2025, 6, 12, 21, 8, 22), 'numero_operacion': '5695993302655', 'msisdn': '51921939356', 'nro_suministro': '921939356', 'registro': 'PEA716C02073482025-06-1221.08.24025520990000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.10')}, {'financial_id': Decimal('8502506122109443885186'), 'payment_time': datetime.datetime(2025, 6, 12, 21, 9, 43), 'numero_operacion': '5695993302688', 'msisdn': '51935888021', 'nro_suministro': '935888021', 'registro': 'PEA716C02092752025-06-1221.09.45025621010000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506122122542876554'), 'payment_time': datetime.datetime(2025, 6, 12, 21, 22, 53), 'numero_operacion': '5695993302922', 'msisdn': '51963675998', 'nro_suministro': '963675998', 'registro': 'PEA716C02221552025-06-1221.22.54025721030000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('28.10')}, {'financial_id': Decimal('5902506122123114056797'), 'payment_time': datetime.datetime(2025, 6, 12, 21, 23, 10), 'numero_operacion': '5695993302893', 'msisdn': '51951404407', 'nro_suministro': '10006948', 'registro': 'PEA716C02212432025-06-1221.23.12025821050000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EMSA_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('27.60')}, {'financial_id': Decimal('5902506122124318416857'), 'payment_time': datetime.datetime(2025, 6, 12, 21, 24, 31), 'numero_operacion': '5695993302934', 'msisdn': '51976055364', 'nro_suministro': '046753755', 'registro': 'PEA716C02240682025-06-1221.24.32025921060000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('95.00')}, {'financial_id': Decimal('8502506122127470996078'), 'payment_time': datetime.datetime(2025, 6, 12, 21, 27, 46), 'numero_operacion': '5695993302971', 'msisdn': '51920299596', 'nro_suministro': '920299596', 'registro': 'PEA716C02265712025-06-1221.27.47026021070000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('33.10')}, {'financial_id': Decimal('5902506122129254837099'), 'payment_time': datetime.datetime(2025, 6, 12, 21, 29, 25), 'numero_operacion': '5695993303016', 'msisdn': '51982016566', 'nro_suministro': '10239722', 'registro': 'PEA716C02285152025-06-1221.29.26026121090000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('232.00')}, {'financial_id': Decimal('1162506122146119337601'), 'payment_time': datetime.datetime(2025, 6, 12, 21, 46, 11), 'numero_operacion': '5695993303264', 'msisdn': '51902804443', 'nro_suministro': '970040766', 'registro': 'PEA716C02452822025-06-1221.46.12026221100000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}]
pie [{'TOTAL WU': 'TOTAL WU', 'SUM(total)': Decimal('37714.27')}]
Creando archivo.
Escribiendo archivo...
Datos del archivo /home/<USER>/output/mysql/PDP-REPORTE-WU_HUB_20250613.csv
Archivo escrito. /home/<USER>/output/mysql/PDP-REPORTE-WU_HUB_20250613.csv
Archivo cerrado. /home/<USER>/output/mysql/PDP-REPORTE-WU_HUB_20250613.csv
Se creo archivo.
PDP-REPORTE-WU_HUB_20250613.csv
