/home/<USER>/output/load_rds
['LOG_TRX_FINAL.csv', 'MTX_TRANSACTION_HEADER.csv']
LOG_TRX_FINAL.csv
Truncando la tabla: LOG_TRX_FINAL
Tabla LOG_TRX_FINAL truncada correctamente.
Traceback (most recent call last):
  File "/home/<USER>/generate/prepare_rds/read_csv_sql.py", line 159, in <module>
    main()
  File "/home/<USER>/generate/prepare_rds/read_csv_sql.py", line 148, in main
    process_file_with_pandas(archivo_path, query)
  File "/home/<USER>/generate/prepare_rds/read_csv_sql.py", line 100, in process_file_with_pandas
    df = read_csv_with_pandas(file_name)
  File "/home/<USER>/generate/prepare_rds/read_csv_sql.py", line 63, in read_csv_with_pandas
    df = pd.read_csv(file_name, delimiter=',', encoding='utf-8', dtype = 'str')
  File "/usr/local/lib64/python3.9/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
  File "/usr/local/lib64/python3.9/site-packages/pandas/io/parsers/readers.py", line 620, in _read
    parser = TextFileReader(filepath_or_buffer, **kwds)
  File "/usr/local/lib64/python3.9/site-packages/pandas/io/parsers/readers.py", line 1620, in __init__
    self._engine = self._make_engine(f, self.engine)
  File "/usr/local/lib64/python3.9/site-packages/pandas/io/parsers/readers.py", line 1898, in _make_engine
    return mapping[engine](f, **self.options)
  File "/usr/local/lib64/python3.9/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 93, in __init__
    self._reader = parsers.TextReader(src, **kwds)
  File "parsers.pyx", line 581, in pandas._libs.parsers.TextReader.__cinit__
pandas.errors.EmptyDataError: No columns to parse from file
