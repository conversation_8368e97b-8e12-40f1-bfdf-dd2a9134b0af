Inicio de la malla
+ export PRIVATE_KEY_PATH=/home/<USER>/generate/FileSigner/pdp_sign.key
+ PRIVATE_KEY_PATH=/home/<USER>/generate/FileSigner/pdp_sign.key
+ export PRIVATE_CRT_PATH=/home/<USER>/generate/FileSigner/SignFileNC.crt
+ PRIVATE_CRT_PATH=/home/<USER>/generate/FileSigner/SignFileNC.crt
+ export OUTPUT_ROUTE=/home/<USER>/output/excel/
+ OUTPUT_ROUTE=/home/<USER>/output/excel/
+ export OUTPUT_ROUTE_CSV=/home/<USER>/output/csv/
+ OUTPUT_ROUTE_CSV=/home/<USER>/output/csv/
+ values32=("32A")
+ values=("BITEL-POST" "BITEL-PRE" "SERVICE-PROVIDER" "SERVICIOS-DIRECTOS" "TRAZA-FEE" "RETIROS" "DEPOSITOS")
+ valuescsv=("32A" "BCRP-NETO-EMISORES" "CRANDES-PAGOS" "VALIDAR-BALANCE" "EQUIVALENCIA-LOG-TRX" "EQUIVALENCIA-PAGOS" "EQUIVALENCIA-AHORROS" "BCRP-OPERACIONES-EMISOR" "BCRP-TIPO-CUENTAS" "AZULITO" "UNIQUE" "MOVISTAR" "ENTEL" "RETIRO-SENTINEL" "RETIRO-WU-HUB" "BCRP-BALANCES" "QR-NIUBIZ" "QR-IZIPAY" "MTX-TRANSACTION" "LOG-TRANSACCIONES")
+ export REPORTS_NO_S3=LOG-TRANSACCIONES,MTX-TRANSACTION,USER-BALANCES,32A
+ REPORTS_NO_S3=LOG-TRANSACCIONES,MTX-TRANSACTION,USER-BALANCES,32A
+ MAX_TIME=1800
+ ROUTE_CSV=/home/<USER>/output/csv
+ TARGET_PATH=/home/<USER>/output/load_rds
+ '[' -z 2025/05/13 ']'
+ fecha=2025/05/13
++ date -d '2025/05/13 + 1 day' +%Y%m%d
+ fecha_path=********
+ cd /home/<USER>/generate/
+ mkdir -p logs/excel logs/csv logs/log_transacciones logs/reports32a_b logs/account_balances logs/interope logs/log_usuarios logs/prepare logs/reporte_conciliacion logs/csv_to_pdf logs/prepare_rds logs/mysql_reports
+ echo '== Generando REPORTES EXCEL =='
== Generando REPORTES EXCEL ==
+ for value in "${values[@]}"
+ run_with_timeout BITEL-POST exports_excel/main.py BITEL-POST 2025/05/13 logs/excel/BITEL-POST.log
+ PROCESS_NAME=BITEL-POST
+ SCRIPT=exports_excel/main.py
+ REPORT=BITEL-POST
+ DATE=2025/05/13
+ LOG_FILE=logs/excel/BITEL-POST.log
+ echo 'Iniciando: BITEL-POST'
Iniciando: BITEL-POST
++ date +%s
+ start_time=**********
+ pid=311873
+ kill -0 311873
+ python3 exports_excel/main.py BITEL-POST 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=85
+ '[' 85 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=90
+ '[' 90 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=95
+ '[' 95 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=100
+ '[' 100 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=105
+ '[' 105 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=110
+ '[' 110 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=115
+ '[' 115 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=120
+ '[' 120 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=125
+ '[' 125 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=130
+ '[' 130 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=135
+ '[' 135 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=140
+ '[' 140 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=145
+ '[' 145 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=150
+ '[' 150 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=155
+ '[' 155 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=160
+ '[' 160 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=165
+ '[' 165 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=170
+ '[' 170 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=175
+ '[' 175 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=180
+ '[' 180 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=185
+ '[' 185 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=190
+ '[' 190 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=195
+ '[' 195 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=200
+ '[' 200 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=205
+ '[' 205 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=210
+ '[' 210 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=215
+ '[' 215 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=220
+ '[' 220 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=225
+ '[' 225 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=230
+ '[' 230 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=235
+ '[' 235 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=240
+ '[' 240 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=245
+ '[' 245 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=250
+ '[' 250 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=255
+ '[' 255 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=260
+ '[' 260 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=265
+ '[' 265 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=270
+ '[' 270 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=275
+ '[' 275 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=280
+ '[' 280 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=285
+ '[' 285 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=290
+ '[' 290 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=295
+ '[' 295 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=300
+ '[' 300 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=305
+ '[' 305 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=310
+ '[' 310 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=315
+ '[' 315 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=320
+ '[' 320 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=325
+ '[' 325 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=330
+ '[' 330 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=335
+ '[' 335 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=340
+ '[' 340 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=345
+ '[' 345 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=350
+ '[' 350 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=355
+ '[' 355 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=360
+ '[' 360 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=365
+ '[' 365 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=370
+ '[' 370 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=375
+ '[' 375 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=380
+ '[' 380 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=385
+ '[' 385 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=390
+ '[' 390 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=395
+ '[' 395 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=400
+ '[' 400 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=405
+ '[' 405 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=410
+ '[' 410 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=415
+ '[' 415 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=420
+ '[' 420 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=425
+ '[' 425 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=430
+ '[' 430 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=435
+ '[' 435 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=440
+ '[' 440 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=445
+ '[' 445 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=450
+ '[' 450 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=455
+ '[' 455 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=460
+ '[' 460 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=465
+ '[' 465 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=470
+ '[' 470 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=475
+ '[' 475 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=480
+ '[' 480 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=485
+ '[' 485 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=490
+ '[' 490 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=495
+ '[' 495 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=500
+ '[' 500 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=505
+ '[' 505 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=510
+ '[' 510 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=515
+ '[' 515 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=520
+ '[' 520 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=525
+ '[' 525 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=530
+ '[' 530 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=535
+ '[' 535 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=540
+ '[' 540 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=545
+ '[' 545 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=550
+ '[' 550 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=555
+ '[' 555 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=560
+ '[' 560 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=565
+ '[' 565 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=570
+ '[' 570 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=575
+ '[' 575 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=580
+ '[' 580 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=585
+ '[' 585 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=590
+ '[' 590 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=595
+ '[' 595 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=600
+ '[' 600 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=605
+ '[' 605 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=610
+ '[' 610 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=615
+ '[' 615 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=620
+ '[' 620 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=625
+ '[' 625 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=630
+ '[' 630 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=635
+ '[' 635 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=640
+ '[' 640 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=645
+ '[' 645 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=650
+ '[' 650 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=655
+ '[' 655 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=660
+ '[' 660 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=665
+ '[' 665 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=670
+ '[' 670 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=675
+ '[' 675 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=680
+ '[' 680 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=685
+ '[' 685 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=690
+ '[' 690 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=695
+ '[' 695 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=700
+ '[' 700 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=705
+ '[' 705 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=710
+ '[' 710 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=715
+ '[' 715 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=720
+ '[' 720 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=725
+ '[' 725 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=730
+ '[' 730 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=735
+ '[' 735 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=740
+ '[' 740 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=745
+ '[' 745 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=750
+ '[' 750 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=755
+ '[' 755 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=760
+ '[' 760 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=765
+ '[' 765 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=770
+ '[' 770 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=775
+ '[' 775 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=780
+ '[' 780 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=785
+ '[' 785 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=790
+ '[' 790 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=795
+ '[' 795 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=800
+ '[' 800 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=805
+ '[' 805 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=810
+ '[' 810 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=815
+ '[' 815 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=820
+ '[' 820 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=825
+ '[' 825 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=830
+ '[' 830 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=835
+ '[' 835 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=840
+ '[' 840 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=845
+ '[' 845 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=850
+ '[' 850 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=855
+ '[' 855 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=860
+ '[' 860 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=865
+ '[' 865 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=870
+ '[' 870 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=875
+ '[' 875 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=880
+ '[' 880 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=885
+ '[' 885 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=890
+ '[' 890 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=895
+ '[' 895 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=900
+ '[' 900 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=905
+ '[' 905 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=910
+ '[' 910 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=915
+ '[' 915 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=920
+ '[' 920 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=925
+ '[' 925 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=930
+ '[' 930 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=935
+ '[' 935 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=940
+ '[' 940 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=945
+ '[' 945 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=950
+ '[' 950 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=955
+ '[' 955 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=960
+ '[' 960 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=965
+ '[' 965 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=970
+ '[' 970 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=975
+ '[' 975 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=980
+ '[' 980 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=985
+ '[' 985 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=990
+ '[' 990 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=995
+ '[' 995 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1000
+ '[' 1000 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1005
+ '[' 1005 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1010
+ '[' 1010 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1015
+ '[' 1015 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1020
+ '[' 1020 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1025
+ '[' 1025 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1030
+ '[' 1030 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1035
+ '[' 1035 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1040
+ '[' 1040 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1045
+ '[' 1045 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1050
+ '[' 1050 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1055
+ '[' 1055 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1060
+ '[' 1060 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1065
+ '[' 1065 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1070
+ '[' 1070 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1075
+ '[' 1075 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1080
+ '[' 1080 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1085
+ '[' 1085 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1090
+ '[' 1090 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1095
+ '[' 1095 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1100
+ '[' 1100 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1105
+ '[' 1105 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1110
+ '[' 1110 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1115
+ '[' 1115 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1120
+ '[' 1120 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1125
+ '[' 1125 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1130
+ '[' 1130 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1135
+ '[' 1135 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1140
+ '[' 1140 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1145
+ '[' 1145 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1150
+ '[' 1150 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1155
+ '[' 1155 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1160
+ '[' 1160 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1165
+ '[' 1165 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1170
+ '[' 1170 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1175
+ '[' 1175 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1180
+ '[' 1180 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1185
+ '[' 1185 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1190
+ '[' 1190 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1195
+ '[' 1195 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1200
+ '[' 1200 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1205
+ '[' 1205 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1210
+ '[' 1210 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1215
+ '[' 1215 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1220
+ '[' 1220 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1225
+ '[' 1225 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1230
+ '[' 1230 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1235
+ '[' 1235 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1240
+ '[' 1240 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1245
+ '[' 1245 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1250
+ '[' 1250 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1255
+ '[' 1255 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1260
+ '[' 1260 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1265
+ '[' 1265 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1270
+ '[' 1270 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1275
+ '[' 1275 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1280
+ '[' 1280 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1285
+ '[' 1285 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1290
+ '[' 1290 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1295
+ '[' 1295 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1300
+ '[' 1300 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1305
+ '[' 1305 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1310
+ '[' 1310 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1315
+ '[' 1315 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1320
+ '[' 1320 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1325
+ '[' 1325 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1330
+ '[' 1330 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1335
+ '[' 1335 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1340
+ '[' 1340 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1345
+ '[' 1345 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1350
+ '[' 1350 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1355
+ '[' 1355 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1360
+ '[' 1360 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1365
+ '[' 1365 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1370
+ '[' 1370 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1375
+ '[' 1375 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1380
+ '[' 1380 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1385
+ '[' 1385 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1390
+ '[' 1390 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1395
+ '[' 1395 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1400
+ '[' 1400 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1405
+ '[' 1405 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1410
+ '[' 1410 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1415
+ '[' 1415 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1420
+ '[' 1420 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1425
+ '[' 1425 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1430
+ '[' 1430 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1435
+ '[' 1435 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1440
+ '[' 1440 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1445
+ '[' 1445 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1450
+ '[' 1450 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1455
+ '[' 1455 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1460
+ '[' 1460 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1465
+ '[' 1465 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1470
+ '[' 1470 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1475
+ '[' 1475 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1480
+ '[' 1480 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1485
+ '[' 1485 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1490
+ '[' 1490 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1495
+ '[' 1495 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1501
+ '[' 1501 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1506
+ '[' 1506 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1511
+ '[' 1511 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1516
+ '[' 1516 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1521
+ '[' 1521 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1526
+ '[' 1526 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1531
+ '[' 1531 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1536
+ '[' 1536 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1541
+ '[' 1541 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1546
+ '[' 1546 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1551
+ '[' 1551 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1556
+ '[' 1556 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1561
+ '[' 1561 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1566
+ '[' 1566 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1571
+ '[' 1571 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1576
+ '[' 1576 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1581
+ '[' 1581 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1586
+ '[' 1586 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1591
+ '[' 1591 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1596
+ '[' 1596 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1601
+ '[' 1601 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1606
+ '[' 1606 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1611
+ '[' 1611 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1616
+ '[' 1616 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1621
+ '[' 1621 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1626
+ '[' 1626 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1631
+ '[' 1631 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1636
+ '[' 1636 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1641
+ '[' 1641 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1646
+ '[' 1646 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1651
+ '[' 1651 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1656
+ '[' 1656 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1661
+ '[' 1661 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1666
+ '[' 1666 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1671
+ '[' 1671 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1676
+ '[' 1676 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1681
+ '[' 1681 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1686
+ '[' 1686 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1691
+ '[' 1691 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1696
+ '[' 1696 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1701
+ '[' 1701 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1706
+ '[' 1706 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1711
+ '[' 1711 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1716
+ '[' 1716 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1721
+ '[' 1721 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1726
+ '[' 1726 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1731
+ '[' 1731 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1736
+ '[' 1736 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1741
+ '[' 1741 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1746
+ '[' 1746 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1751
+ '[' 1751 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1756
+ '[' 1756 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1761
+ '[' 1761 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1766
+ '[' 1766 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1771
+ '[' 1771 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1776
+ '[' 1776 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1781
+ '[' 1781 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1786
+ '[' 1786 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1791
+ '[' 1791 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1796
+ '[' 1796 -gt 1800 ']'
+ sleep 5
+ kill -0 311873
++ date +%s
+ elapsed_time=1801
+ '[' 1801 -gt 1800 ']'
+ echo 'El proceso BITEL-POST excedió el tiempo límite de 1800 segundos. Terminando...'
El proceso BITEL-POST excedió el tiempo límite de 1800 segundos. Terminando...
+ kill -9 311873
+ echo 'Proceso terminado por timeout después de 1801 segundos'
+ break
+ wait 311873
/home/<USER>/generate/ejecuta_diario.sh: line 48: 311873 Killed                  python3 $SCRIPT $REPORT $DATE > "$LOG_FILE" 2>&1
+ return_code=137
+ '[' 137 -eq 0 ']'
+ echo '❌ BITEL-POST falló (código: 137)'
❌ BITEL-POST falló (código: 137)
+ for value in "${values[@]}"
+ run_with_timeout BITEL-PRE exports_excel/main.py BITEL-PRE 2025/05/13 logs/excel/BITEL-PRE.log
+ PROCESS_NAME=BITEL-PRE
+ SCRIPT=exports_excel/main.py
+ REPORT=BITEL-PRE
+ DATE=2025/05/13
+ LOG_FILE=logs/excel/BITEL-PRE.log
+ echo 'Iniciando: BITEL-PRE'
Iniciando: BITEL-PRE
++ date +%s
+ start_time=1747202403
+ pid=313581
+ kill -0 313581
+ python3 exports_excel/main.py BITEL-PRE 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=85
+ '[' 85 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=90
+ '[' 90 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=95
+ '[' 95 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=100
+ '[' 100 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=105
+ '[' 105 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=110
+ '[' 110 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=115
+ '[' 115 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=120
+ '[' 120 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=125
+ '[' 125 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=130
+ '[' 130 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=135
+ '[' 135 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=140
+ '[' 140 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=145
+ '[' 145 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=150
+ '[' 150 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=155
+ '[' 155 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=160
+ '[' 160 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=165
+ '[' 165 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=170
+ '[' 170 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=175
+ '[' 175 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=180
+ '[' 180 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=185
+ '[' 185 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=190
+ '[' 190 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=195
+ '[' 195 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=200
+ '[' 200 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=205
+ '[' 205 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=210
+ '[' 210 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=215
+ '[' 215 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=220
+ '[' 220 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=225
+ '[' 225 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=230
+ '[' 230 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=235
+ '[' 235 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=240
+ '[' 240 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=245
+ '[' 245 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=250
+ '[' 250 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=255
+ '[' 255 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=260
+ '[' 260 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=265
+ '[' 265 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=270
+ '[' 270 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
++ date +%s
+ elapsed_time=275
+ '[' 275 -gt 1800 ']'
+ sleep 5
+ kill -0 313581
+ wait 313581
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ BITEL-PRE completado exitosamente'
✅ BITEL-PRE completado exitosamente
+ for value in "${values[@]}"
+ run_with_timeout SERVICE-PROVIDER exports_excel/main.py SERVICE-PROVIDER 2025/05/13 logs/excel/SERVICE-PROVIDER.log
+ PROCESS_NAME=SERVICE-PROVIDER
+ SCRIPT=exports_excel/main.py
+ REPORT=SERVICE-PROVIDER
+ DATE=2025/05/13
+ LOG_FILE=logs/excel/SERVICE-PROVIDER.log
+ echo 'Iniciando: SERVICE-PROVIDER'
Iniciando: SERVICE-PROVIDER
++ date +%s
+ start_time=**********
+ pid=313905
+ kill -0 313905
+ python3 exports_excel/main.py SERVICE-PROVIDER 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 313905
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 313905
+ wait 313905
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ SERVICE-PROVIDER completado exitosamente'
✅ SERVICE-PROVIDER completado exitosamente
+ for value in "${values[@]}"
+ run_with_timeout SERVICIOS-DIRECTOS exports_excel/main.py SERVICIOS-DIRECTOS 2025/05/13 logs/excel/SERVICIOS-DIRECTOS.log
+ PROCESS_NAME=SERVICIOS-DIRECTOS
+ SCRIPT=exports_excel/main.py
+ REPORT=SERVICIOS-DIRECTOS
+ DATE=2025/05/13
+ LOG_FILE=logs/excel/SERVICIOS-DIRECTOS.log
+ echo 'Iniciando: SERVICIOS-DIRECTOS'
Iniciando: SERVICIOS-DIRECTOS
++ date +%s
+ start_time=**********
+ pid=313920
+ kill -0 313920
+ python3 exports_excel/main.py SERVICIOS-DIRECTOS 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 313920
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 313920
+ wait 313920
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ SERVICIOS-DIRECTOS completado exitosamente'
✅ SERVICIOS-DIRECTOS completado exitosamente
+ for value in "${values[@]}"
+ run_with_timeout TRAZA-FEE exports_excel/main.py TRAZA-FEE 2025/05/13 logs/excel/TRAZA-FEE.log
+ PROCESS_NAME=TRAZA-FEE
+ SCRIPT=exports_excel/main.py
+ REPORT=TRAZA-FEE
+ DATE=2025/05/13
+ LOG_FILE=logs/excel/TRAZA-FEE.log
+ echo 'Iniciando: TRAZA-FEE'
Iniciando: TRAZA-FEE
++ date +%s
+ start_time=1747202703
+ pid=313935
+ kill -0 313935
+ python3 exports_excel/main.py TRAZA-FEE 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 313935
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 313935
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 313935
+ wait 313935
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ TRAZA-FEE completado exitosamente'
✅ TRAZA-FEE completado exitosamente
+ for value in "${values[@]}"
+ run_with_timeout RETIROS exports_excel/main.py RETIROS 2025/05/13 logs/excel/RETIROS.log
+ PROCESS_NAME=RETIROS
+ SCRIPT=exports_excel/main.py
+ REPORT=RETIROS
+ DATE=2025/05/13
+ LOG_FILE=logs/excel/RETIROS.log
+ echo 'Iniciando: RETIROS'
Iniciando: RETIROS
++ date +%s
+ start_time=1747202718
+ pid=313975
+ kill -0 313975
+ python3 exports_excel/main.py RETIROS 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 313975
+ wait 313975
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ RETIROS completado exitosamente'
✅ RETIROS completado exitosamente
+ for value in "${values[@]}"
+ run_with_timeout DEPOSITOS exports_excel/main.py DEPOSITOS 2025/05/13 logs/excel/DEPOSITOS.log
+ PROCESS_NAME=DEPOSITOS
+ SCRIPT=exports_excel/main.py
+ REPORT=DEPOSITOS
+ DATE=2025/05/13
+ LOG_FILE=logs/excel/DEPOSITOS.log
+ echo 'Iniciando: DEPOSITOS'
Iniciando: DEPOSITOS
++ date +%s
+ start_time=1747202723
+ pid=314014
+ kill -0 314014
+ python3 exports_excel/main.py DEPOSITOS 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314014
+ wait 314014
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ DEPOSITOS completado exitosamente'
✅ DEPOSITOS completado exitosamente
+ echo '== Generando REPORTES CSV =='
== Generando REPORTES CSV ==
+ for value in "${valuescsv[@]}"
+ run_with_timeout 32A exports_csv/main.py 32A 2025/05/13 logs/csv/32A.log
+ PROCESS_NAME=32A
+ SCRIPT=exports_csv/main.py
+ REPORT=32A
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/32A.log
+ echo 'Iniciando: 32A'
Iniciando: 32A
++ date +%s
+ start_time=1747202728
+ pid=314049
+ kill -0 314049
+ python3 exports_csv/main.py 32A 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314049
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314049
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 314049
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 314049
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 314049
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 314049
+ wait 314049
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ 32A completado exitosamente'
✅ 32A completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout BCRP-NETO-EMISORES exports_csv/main.py BCRP-NETO-EMISORES 2025/05/13 logs/csv/BCRP-NETO-EMISORES.log
+ PROCESS_NAME=BCRP-NETO-EMISORES
+ SCRIPT=exports_csv/main.py
+ REPORT=BCRP-NETO-EMISORES
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/BCRP-NETO-EMISORES.log
+ echo 'Iniciando: BCRP-NETO-EMISORES'
Iniciando: BCRP-NETO-EMISORES
++ date +%s
+ start_time=1747202758
+ pid=314073
+ kill -0 314073
+ python3 exports_csv/main.py BCRP-NETO-EMISORES 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314073
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314073
+ wait 314073
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ BCRP-NETO-EMISORES completado exitosamente'
✅ BCRP-NETO-EMISORES completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout CRANDES-PAGOS exports_csv/main.py CRANDES-PAGOS 2025/05/13 logs/csv/CRANDES-PAGOS.log
+ PROCESS_NAME=CRANDES-PAGOS
+ SCRIPT=exports_csv/main.py
+ REPORT=CRANDES-PAGOS
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/CRANDES-PAGOS.log
+ echo 'Iniciando: CRANDES-PAGOS'
Iniciando: CRANDES-PAGOS
++ date +%s
+ start_time=1747202768
+ pid=314088
+ kill -0 314088
+ python3 exports_csv/main.py CRANDES-PAGOS 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314088
+ wait 314088
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ CRANDES-PAGOS completado exitosamente'
✅ CRANDES-PAGOS completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout VALIDAR-BALANCE exports_csv/main.py VALIDAR-BALANCE 2025/05/13 logs/csv/VALIDAR-BALANCE.log
+ PROCESS_NAME=VALIDAR-BALANCE
+ SCRIPT=exports_csv/main.py
+ REPORT=VALIDAR-BALANCE
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/VALIDAR-BALANCE.log
+ echo 'Iniciando: VALIDAR-BALANCE'
Iniciando: VALIDAR-BALANCE
++ date +%s
+ start_time=1747202773
+ pid=314160
+ kill -0 314160
+ python3 exports_csv/main.py VALIDAR-BALANCE 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=85
+ '[' 85 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=90
+ '[' 90 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=95
+ '[' 95 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
++ date +%s
+ elapsed_time=100
+ '[' 100 -gt 1800 ']'
+ sleep 5
+ kill -0 314160
+ wait 314160
+ return_code=1
+ '[' 1 -eq 0 ']'
+ echo '❌ VALIDAR-BALANCE falló (código: 1)'
❌ VALIDAR-BALANCE falló (código: 1)
+ for value in "${valuescsv[@]}"
+ run_with_timeout EQUIVALENCIA-LOG-TRX exports_csv/main.py EQUIVALENCIA-LOG-TRX 2025/05/13 logs/csv/EQUIVALENCIA-LOG-TRX.log
+ PROCESS_NAME=EQUIVALENCIA-LOG-TRX
+ SCRIPT=exports_csv/main.py
+ REPORT=EQUIVALENCIA-LOG-TRX
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/EQUIVALENCIA-LOG-TRX.log
+ echo 'Iniciando: EQUIVALENCIA-LOG-TRX'
Iniciando: EQUIVALENCIA-LOG-TRX
++ date +%s
+ start_time=1747202878
+ pid=314213
+ kill -0 314213
+ python3 exports_csv/main.py EQUIVALENCIA-LOG-TRX 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314213
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314213
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 314213
+ wait 314213
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ EQUIVALENCIA-LOG-TRX completado exitosamente'
✅ EQUIVALENCIA-LOG-TRX completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout EQUIVALENCIA-PAGOS exports_csv/main.py EQUIVALENCIA-PAGOS 2025/05/13 logs/csv/EQUIVALENCIA-PAGOS.log
+ PROCESS_NAME=EQUIVALENCIA-PAGOS
+ SCRIPT=exports_csv/main.py
+ REPORT=EQUIVALENCIA-PAGOS
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/EQUIVALENCIA-PAGOS.log
+ echo 'Iniciando: EQUIVALENCIA-PAGOS'
Iniciando: EQUIVALENCIA-PAGOS
++ date +%s
+ start_time=1747202893
+ pid=314292
+ kill -0 314292
+ python3 exports_csv/main.py EQUIVALENCIA-PAGOS 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314292
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314292
+ wait 314292
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ EQUIVALENCIA-PAGOS completado exitosamente'
✅ EQUIVALENCIA-PAGOS completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout EQUIVALENCIA-AHORROS exports_csv/main.py EQUIVALENCIA-AHORROS 2025/05/13 logs/csv/EQUIVALENCIA-AHORROS.log
+ PROCESS_NAME=EQUIVALENCIA-AHORROS
+ SCRIPT=exports_csv/main.py
+ REPORT=EQUIVALENCIA-AHORROS
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/EQUIVALENCIA-AHORROS.log
+ echo 'Iniciando: EQUIVALENCIA-AHORROS'
Iniciando: EQUIVALENCIA-AHORROS
++ date +%s
+ start_time=1747202903
+ pid=314307
+ kill -0 314307
+ python3 exports_csv/main.py EQUIVALENCIA-AHORROS 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314307
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314307
+ wait 314307
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ EQUIVALENCIA-AHORROS completado exitosamente'
✅ EQUIVALENCIA-AHORROS completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout BCRP-OPERACIONES-EMISOR exports_csv/main.py BCRP-OPERACIONES-EMISOR 2025/05/13 logs/csv/BCRP-OPERACIONES-EMISOR.log
+ PROCESS_NAME=BCRP-OPERACIONES-EMISOR
+ SCRIPT=exports_csv/main.py
+ REPORT=BCRP-OPERACIONES-EMISOR
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/BCRP-OPERACIONES-EMISOR.log
+ echo 'Iniciando: BCRP-OPERACIONES-EMISOR'
Iniciando: BCRP-OPERACIONES-EMISOR
++ date +%s
+ start_time=1747202913
+ pid=314323
+ kill -0 314323
+ python3 exports_csv/main.py BCRP-OPERACIONES-EMISOR 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314323
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314323
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 314323
+ wait 314323
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ BCRP-OPERACIONES-EMISOR completado exitosamente'
✅ BCRP-OPERACIONES-EMISOR completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout BCRP-TIPO-CUENTAS exports_csv/main.py BCRP-TIPO-CUENTAS 2025/05/13 logs/csv/BCRP-TIPO-CUENTAS.log
+ PROCESS_NAME=BCRP-TIPO-CUENTAS
+ SCRIPT=exports_csv/main.py
+ REPORT=BCRP-TIPO-CUENTAS
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/BCRP-TIPO-CUENTAS.log
+ echo 'Iniciando: BCRP-TIPO-CUENTAS'
Iniciando: BCRP-TIPO-CUENTAS
++ date +%s
+ start_time=1747202928
+ pid=314341
+ kill -0 314341
+ python3 exports_csv/main.py BCRP-TIPO-CUENTAS 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314341
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314341
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 314341
+ wait 314341
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ BCRP-TIPO-CUENTAS completado exitosamente'
✅ BCRP-TIPO-CUENTAS completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout AZULITO exports_csv/main.py AZULITO 2025/05/13 logs/csv/AZULITO.log
+ PROCESS_NAME=AZULITO
+ SCRIPT=exports_csv/main.py
+ REPORT=AZULITO
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/AZULITO.log
+ echo 'Iniciando: AZULITO'
Iniciando: AZULITO
++ date +%s
+ start_time=1747202943
+ pid=314359
+ kill -0 314359
+ python3 exports_csv/main.py AZULITO 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314359
+ wait 314359
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ AZULITO completado exitosamente'
✅ AZULITO completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout UNIQUE exports_csv/main.py UNIQUE 2025/05/13 logs/csv/UNIQUE.log
+ PROCESS_NAME=UNIQUE
+ SCRIPT=exports_csv/main.py
+ REPORT=UNIQUE
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/UNIQUE.log
+ echo 'Iniciando: UNIQUE'
Iniciando: UNIQUE
++ date +%s
+ start_time=1747202948
+ pid=314372
+ kill -0 314372
+ python3 exports_csv/main.py UNIQUE 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314372
+ wait 314372
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ UNIQUE completado exitosamente'
✅ UNIQUE completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout MOVISTAR exports_csv/main.py MOVISTAR 2025/05/13 logs/csv/MOVISTAR.log
+ PROCESS_NAME=MOVISTAR
+ SCRIPT=exports_csv/main.py
+ REPORT=MOVISTAR
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/MOVISTAR.log
+ echo 'Iniciando: MOVISTAR'
Iniciando: MOVISTAR
++ date +%s
+ start_time=1747202953
+ pid=314385
+ kill -0 314385
+ python3 exports_csv/main.py MOVISTAR 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314385
+ wait 314385
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ MOVISTAR completado exitosamente'
✅ MOVISTAR completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout ENTEL exports_csv/main.py ENTEL 2025/05/13 logs/csv/ENTEL.log
+ PROCESS_NAME=ENTEL
+ SCRIPT=exports_csv/main.py
+ REPORT=ENTEL
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/ENTEL.log
+ echo 'Iniciando: ENTEL'
Iniciando: ENTEL
++ date +%s
+ start_time=1747202958
+ pid=314398
+ kill -0 314398
+ python3 exports_csv/main.py ENTEL 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314398
+ wait 314398
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ ENTEL completado exitosamente'
✅ ENTEL completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout RETIRO-SENTINEL exports_csv/main.py RETIRO-SENTINEL 2025/05/13 logs/csv/RETIRO-SENTINEL.log
+ PROCESS_NAME=RETIRO-SENTINEL
+ SCRIPT=exports_csv/main.py
+ REPORT=RETIRO-SENTINEL
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/RETIRO-SENTINEL.log
+ echo 'Iniciando: RETIRO-SENTINEL'
Iniciando: RETIRO-SENTINEL
++ date +%s
+ start_time=1747202963
+ pid=314411
+ kill -0 314411
+ python3 exports_csv/main.py RETIRO-SENTINEL 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314411
+ wait 314411
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ RETIRO-SENTINEL completado exitosamente'
✅ RETIRO-SENTINEL completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout RETIRO-WU-HUB exports_csv/main.py RETIRO-WU-HUB 2025/05/13 logs/csv/RETIRO-WU-HUB.log
+ PROCESS_NAME=RETIRO-WU-HUB
+ SCRIPT=exports_csv/main.py
+ REPORT=RETIRO-WU-HUB
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/RETIRO-WU-HUB.log
+ echo 'Iniciando: RETIRO-WU-HUB'
Iniciando: RETIRO-WU-HUB
++ date +%s
+ start_time=1747202968
+ pid=314424
+ kill -0 314424
+ python3 exports_csv/main.py RETIRO-WU-HUB 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314424
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314424
+ wait 314424
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ RETIRO-WU-HUB completado exitosamente'
✅ RETIRO-WU-HUB completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout BCRP-BALANCES exports_csv/main.py BCRP-BALANCES 2025/05/13 logs/csv/BCRP-BALANCES.log
+ PROCESS_NAME=BCRP-BALANCES
+ SCRIPT=exports_csv/main.py
+ REPORT=BCRP-BALANCES
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/BCRP-BALANCES.log
+ echo 'Iniciando: BCRP-BALANCES'
Iniciando: BCRP-BALANCES
++ date +%s
+ start_time=1747202978
+ pid=314440
+ kill -0 314440
+ python3 exports_csv/main.py BCRP-BALANCES 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314440
+ wait 314440
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ BCRP-BALANCES completado exitosamente'
✅ BCRP-BALANCES completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout QR-NIUBIZ exports_csv/main.py QR-NIUBIZ 2025/05/13 logs/csv/QR-NIUBIZ.log
+ PROCESS_NAME=QR-NIUBIZ
+ SCRIPT=exports_csv/main.py
+ REPORT=QR-NIUBIZ
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/QR-NIUBIZ.log
+ echo 'Iniciando: QR-NIUBIZ'
Iniciando: QR-NIUBIZ
++ date +%s
+ start_time=1747202983
+ pid=314453
+ kill -0 314453
+ python3 exports_csv/main.py QR-NIUBIZ 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314453
+ wait 314453
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ QR-NIUBIZ completado exitosamente'
✅ QR-NIUBIZ completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout QR-IZIPAY exports_csv/main.py QR-IZIPAY 2025/05/13 logs/csv/QR-IZIPAY.log
+ PROCESS_NAME=QR-IZIPAY
+ SCRIPT=exports_csv/main.py
+ REPORT=QR-IZIPAY
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/QR-IZIPAY.log
+ echo 'Iniciando: QR-IZIPAY'
Iniciando: QR-IZIPAY
++ date +%s
+ start_time=1747202988
+ pid=314466
+ kill -0 314466
+ python3 exports_csv/main.py QR-IZIPAY 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314466
+ wait 314466
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ QR-IZIPAY completado exitosamente'
✅ QR-IZIPAY completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout MTX-TRANSACTION exports_csv/main.py MTX-TRANSACTION 2025/05/13 logs/csv/MTX-TRANSACTION.log
+ PROCESS_NAME=MTX-TRANSACTION
+ SCRIPT=exports_csv/main.py
+ REPORT=MTX-TRANSACTION
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/MTX-TRANSACTION.log
+ echo 'Iniciando: MTX-TRANSACTION'
Iniciando: MTX-TRANSACTION
++ date +%s
+ start_time=1747202993
+ pid=314479
+ kill -0 314479
+ python3 exports_csv/main.py MTX-TRANSACTION 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1800 ']'
+ sleep 5
+ kill -0 314479
+ wait 314479
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ MTX-TRANSACTION completado exitosamente'
✅ MTX-TRANSACTION completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout LOG-TRANSACCIONES exports_csv/main.py LOG-TRANSACCIONES 2025/05/13 logs/csv/LOG-TRANSACCIONES.log
+ PROCESS_NAME=LOG-TRANSACCIONES
+ SCRIPT=exports_csv/main.py
+ REPORT=LOG-TRANSACCIONES
+ DATE=2025/05/13
+ LOG_FILE=logs/csv/LOG-TRANSACCIONES.log
+ echo 'Iniciando: LOG-TRANSACCIONES'
Iniciando: LOG-TRANSACCIONES
++ date +%s
+ start_time=1747203078
+ pid=314587
+ kill -0 314587
+ python3 exports_csv/main.py LOG-TRANSACCIONES 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314587
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314587
+ wait 314587
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ LOG-TRANSACCIONES completado exitosamente'
✅ LOG-TRANSACCIONES completado exitosamente
+ echo '== Procesando LOG TRANSACCIONES =='
== Procesando LOG TRANSACCIONES ==
+ cd /home/<USER>/generate/log_transacciones/
+ mkdir -p output/********
+ run_with_timeout LOG-TRANSACCIONES-PROCESAR procesar.py '' 2025/05/13 /home/<USER>/generate/logs/log_transacciones/LOG-TRANSACCIONES.log
+ PROCESS_NAME=LOG-TRANSACCIONES-PROCESAR
+ SCRIPT=procesar.py
+ REPORT=
+ DATE=2025/05/13
+ LOG_FILE=/home/<USER>/generate/logs/log_transacciones/LOG-TRANSACCIONES.log
+ echo 'Iniciando: LOG-TRANSACCIONES-PROCESAR'
Iniciando: LOG-TRANSACCIONES-PROCESAR
++ date +%s
+ start_time=**********
+ pid=314601
+ kill -0 314601
+ python3 procesar.py 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314601
+ wait 314601
+ return_code=1
+ '[' 1 -eq 0 ']'
+ echo '❌ LOG-TRANSACCIONES-PROCESAR falló (código: 1)'
❌ LOG-TRANSACCIONES-PROCESAR falló (código: 1)
+ echo '== Procesando ACCOUNT BALANCES =='
== Procesando ACCOUNT BALANCES ==
+ cd /home/<USER>/generate/account_balance/
+ run_with_timeout ACCOUNT-BALANCES main.py '' 2025/05/13 /home/<USER>/generate/logs/account_balances/ACC-BALANCES.log
+ PROCESS_NAME=ACCOUNT-BALANCES
+ SCRIPT=main.py
+ REPORT=
+ DATE=2025/05/13
+ LOG_FILE=/home/<USER>/generate/logs/account_balances/ACC-BALANCES.log
+ echo 'Iniciando: ACCOUNT-BALANCES'
Iniciando: ACCOUNT-BALANCES
++ date +%s
+ start_time=**********
+ pid=314612
+ kill -0 314612
+ python3 main.py 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314612
+ wait 314612
+ return_code=1
+ '[' 1 -eq 0 ']'
+ echo '❌ ACCOUNT-BALANCES falló (código: 1)'
❌ ACCOUNT-BALANCES falló (código: 1)
+ echo '== Generando REPORTES 32x =='
== Generando REPORTES 32x ==
+ cd /home/<USER>/generate/reports32a-b/
+ for value in "${values32[@]}"
+ echo 'Iniciando: 32A'
Iniciando: 32A
+ run_with_timeout 32A-32 main.py 32A 2025/05/13 /home/<USER>/generate/logs/reports32a_b/32A.log
+ PROCESS_NAME=32A-32
+ SCRIPT=main.py
+ REPORT=32A
+ DATE=2025/05/13
+ LOG_FILE=/home/<USER>/generate/logs/reports32a_b/32A.log
+ echo 'Iniciando: 32A-32'
Iniciando: 32A-32
++ date +%s
+ start_time=**********
+ pid=314623
+ kill -0 314623
+ python3 main.py 32A 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314623
+ wait 314623
+ return_code=1
+ '[' 1 -eq 0 ']'
+ echo '❌ 32A-32 falló (código: 1)'
❌ 32A-32 falló (código: 1)
+ echo '== Ejecutando GOPAY =='
== Ejecutando GOPAY ==
+ cd /home/<USER>/generate/mysql_reports/GOPAY
+ run_with_timeout GOPAY main.py '' 2025/05/13 /home/<USER>/generate/logs/mysql_reports/GOPAY.log
+ PROCESS_NAME=GOPAY
+ SCRIPT=main.py
+ REPORT=
+ DATE=2025/05/13
+ LOG_FILE=/home/<USER>/generate/logs/mysql_reports/GOPAY.log
+ echo 'Iniciando: GOPAY'
Iniciando: GOPAY
++ date +%s
+ start_time=1747203103
+ pid=314635
+ kill -0 314635
+ python3 main.py 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314635
+ wait 314635
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ GOPAY completado exitosamente'
✅ GOPAY completado exitosamente
+ echo '== Ejecutando FULLCARGAS =='
== Ejecutando FULLCARGAS ==
+ cd /home/<USER>/generate/mysql_reports/Fullcargas
+ run_with_timeout FULLCARGAS main.py '' 2025/05/13 /home/<USER>/generate/logs/mysql_reports/FULLCARGAS.log
+ PROCESS_NAME=FULLCARGAS
+ SCRIPT=main.py
+ REPORT=
+ DATE=2025/05/13
+ LOG_FILE=/home/<USER>/generate/logs/mysql_reports/FULLCARGAS.log
+ echo 'Iniciando: FULLCARGAS'
Iniciando: FULLCARGAS
++ date +%s
+ start_time=1747203108
+ pid=314641
+ kill -0 314641
+ python3 main.py 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314641
+ wait 314641
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ FULLCARGAS completado exitosamente'
✅ FULLCARGAS completado exitosamente
+ echo '== Ejecutando SERVICIOS-WU =='
== Ejecutando SERVICIOS-WU ==
+ cd /home/<USER>/generate/mysql_reports/Servicios-WU
+ run_with_timeout SERVICIOS-WU main.py '' 2025/05/13 /home/<USER>/generate/logs/mysql_reports/SERVICIOS-WU.log
+ PROCESS_NAME=SERVICIOS-WU
+ SCRIPT=main.py
+ REPORT=
+ DATE=2025/05/13
+ LOG_FILE=/home/<USER>/generate/logs/mysql_reports/SERVICIOS-WU.log
+ echo 'Iniciando: SERVICIOS-WU'
Iniciando: SERVICIOS-WU
++ date +%s
+ start_time=1747203113
+ pid=314647
+ kill -0 314647
+ python3 main.py 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314647
+ wait 314647
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ SERVICIOS-WU completado exitosamente'
✅ SERVICIOS-WU completado exitosamente
+ echo '== Ejecutando CONCILIACION BIM =='
== Ejecutando CONCILIACION BIM ==
+ cd /home/<USER>/generate/reporte_conciliacion/
+ run_with_timeout CONCILIACION-BIM main.py '' 2025/05/13 /home/<USER>/generate/logs/reporte_conciliacion/CONCILIACION-BIM.log
+ PROCESS_NAME=CONCILIACION-BIM
+ SCRIPT=main.py
+ REPORT=
+ DATE=2025/05/13
+ LOG_FILE=/home/<USER>/generate/logs/reporte_conciliacion/CONCILIACION-BIM.log
+ echo 'Iniciando: CONCILIACION-BIM'
Iniciando: CONCILIACION-BIM
++ date +%s
+ start_time=1747203118
+ pid=314653
+ kill -0 314653
+ python3 main.py 2025/05/13
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=85
+ '[' 85 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=90
+ '[' 90 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=95
+ '[' 95 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=100
+ '[' 100 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=105
+ '[' 105 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=110
+ '[' 110 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=115
+ '[' 115 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=120
+ '[' 120 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=125
+ '[' 125 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=130
+ '[' 130 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=135
+ '[' 135 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=140
+ '[' 140 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=145
+ '[' 145 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=150
+ '[' 150 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=155
+ '[' 155 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=160
+ '[' 160 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=165
+ '[' 165 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=170
+ '[' 170 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=175
+ '[' 175 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=180
+ '[' 180 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=185
+ '[' 185 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=190
+ '[' 190 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=195
+ '[' 195 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=200
+ '[' 200 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=205
+ '[' 205 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=210
+ '[' 210 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=215
+ '[' 215 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=220
+ '[' 220 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=225
+ '[' 225 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=230
+ '[' 230 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=235
+ '[' 235 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=240
+ '[' 240 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=245
+ '[' 245 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=250
+ '[' 250 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=255
+ '[' 255 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=260
+ '[' 260 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=265
+ '[' 265 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=270
+ '[' 270 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=275
+ '[' 275 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=280
+ '[' 280 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=285
+ '[' 285 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=290
+ '[' 290 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=295
+ '[' 295 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=300
+ '[' 300 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=305
+ '[' 305 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=310
+ '[' 310 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=315
+ '[' 315 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=320
+ '[' 320 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=325
+ '[' 325 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=330
+ '[' 330 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=335
+ '[' 335 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=340
+ '[' 340 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=345
+ '[' 345 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=350
+ '[' 350 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=355
+ '[' 355 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=360
+ '[' 360 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=365
+ '[' 365 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=370
+ '[' 370 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=375
+ '[' 375 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=380
+ '[' 380 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=385
+ '[' 385 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=390
+ '[' 390 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=395
+ '[' 395 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=400
+ '[' 400 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=405
+ '[' 405 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=410
+ '[' 410 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=415
+ '[' 415 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=420
+ '[' 420 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=425
+ '[' 425 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=430
+ '[' 430 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=435
+ '[' 435 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=440
+ '[' 440 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=445
+ '[' 445 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=450
+ '[' 450 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=455
+ '[' 455 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=460
+ '[' 460 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=465
+ '[' 465 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=470
+ '[' 470 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=475
+ '[' 475 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=480
+ '[' 480 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=485
+ '[' 485 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=490
+ '[' 490 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=495
+ '[' 495 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=500
+ '[' 500 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=505
+ '[' 505 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=510
+ '[' 510 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=515
+ '[' 515 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=520
+ '[' 520 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=525
+ '[' 525 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=530
+ '[' 530 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=535
+ '[' 535 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=540
+ '[' 540 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=545
+ '[' 545 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=550
+ '[' 550 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=555
+ '[' 555 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=560
+ '[' 560 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=565
+ '[' 565 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=570
+ '[' 570 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=575
+ '[' 575 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=580
+ '[' 580 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=585
+ '[' 585 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=590
+ '[' 590 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=595
+ '[' 595 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=600
+ '[' 600 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=605
+ '[' 605 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=610
+ '[' 610 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=615
+ '[' 615 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=620
+ '[' 620 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=625
+ '[' 625 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=630
+ '[' 630 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=635
+ '[' 635 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=640
+ '[' 640 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=645
+ '[' 645 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=650
+ '[' 650 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=655
+ '[' 655 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=660
+ '[' 660 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=665
+ '[' 665 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=670
+ '[' 670 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=675
+ '[' 675 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=680
+ '[' 680 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=685
+ '[' 685 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=690
+ '[' 690 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=695
+ '[' 695 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=700
+ '[' 700 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=705
+ '[' 705 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=710
+ '[' 710 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=715
+ '[' 715 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=720
+ '[' 720 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=726
+ '[' 726 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=731
+ '[' 731 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=736
+ '[' 736 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=741
+ '[' 741 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=746
+ '[' 746 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=751
+ '[' 751 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=756
+ '[' 756 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=761
+ '[' 761 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=766
+ '[' 766 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=771
+ '[' 771 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=776
+ '[' 776 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=781
+ '[' 781 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=786
+ '[' 786 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=791
+ '[' 791 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=796
+ '[' 796 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=801
+ '[' 801 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=806
+ '[' 806 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=811
+ '[' 811 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=816
+ '[' 816 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=821
+ '[' 821 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=826
+ '[' 826 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=831
+ '[' 831 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=836
+ '[' 836 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
++ date +%s
+ elapsed_time=841
+ '[' 841 -gt 1800 ']'
+ sleep 5
+ kill -0 314653
+ wait 314653
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ CONCILIACION-BIM completado exitosamente'
✅ CONCILIACION-BIM completado exitosamente
+ echo '== Ejecutando REPORTE INTEROPERABILIDAD NIUBIZ =='
== Ejecutando REPORTE INTEROPERABILIDAD NIUBIZ ==
+ log_trx_file=TR-********.csv
+ mtx_trx_header_file=MTX_TRANSACTION_HEADER_********.csv
+ log_trx_new_name=LOG_TRX_FINAL.csv
+ mtx_trx_new_name=MTX_TRANSACTION_HEADER.csv
+ mkdir -p /home/<USER>/output/load_rds
+ '[' -f /home/<USER>/output/csv/TR-********.csv ']'
+ mv /home/<USER>/output/csv/TR-********.csv /home/<USER>/output/load_rds/LOG_TRX_FINAL.csv
+ echo '✅ Archivo TR-********.csv movido a LOG_TRX_FINAL.csv'
✅ Archivo TR-********.csv movido a LOG_TRX_FINAL.csv
+ '[' -f /home/<USER>/output/csv/MTX_TRANSACTION_HEADER_********.csv ']'
+ mv /home/<USER>/output/csv/MTX_TRANSACTION_HEADER_********.csv /home/<USER>/output/load_rds/MTX_TRANSACTION_HEADER.csv
+ echo '✅ Archivo MTX_TRANSACTION_HEADER_********.csv movido a MTX_TRANSACTION_HEADER.csv'
✅ Archivo MTX_TRANSACTION_HEADER_********.csv movido a MTX_TRANSACTION_HEADER.csv
+ cd /home/<USER>/generate/prepare_rds/
+ run_with_timeout CARGAR-RDS read_csv_sql.py /home/<USER>/output/load_rds '' /home/<USER>/generate/logs/prepare_rds/CARGAR-RDS.log
+ PROCESS_NAME=CARGAR-RDS
+ SCRIPT=read_csv_sql.py
+ REPORT=/home/<USER>/output/load_rds
+ DATE=
+ LOG_FILE=/home/<USER>/generate/logs/prepare_rds/CARGAR-RDS.log
+ echo 'Iniciando: CARGAR-RDS'
Iniciando: CARGAR-RDS
++ date +%s
+ start_time=1747203964
+ pid=315461
+ kill -0 315461
+ python3 read_csv_sql.py /home/<USER>/output/load_rds
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 315461
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 315461
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 315461
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 315461
+ wait 315461
+ return_code=1
+ '[' 1 -eq 0 ']'
+ echo '❌ CARGAR-RDS falló (código: 1)'
❌ CARGAR-RDS falló (código: 1)
+ cd /home/<USER>/generate/
+ echo ''

+ echo '== RESUMEN DE ESTADO =='
== RESUMEN DE ESTADO ==
+ for value in "${values[@]}"
+ grep -qi error logs/excel/BITEL-POST.log
+ echo '✅ BITEL-POST'
✅ BITEL-POST
+ for value in "${values[@]}"
+ grep -qi error logs/excel/BITEL-PRE.log
+ echo '✅ BITEL-PRE'
✅ BITEL-PRE
+ for value in "${values[@]}"
+ grep -qi error logs/excel/SERVICE-PROVIDER.log
+ echo '✅ SERVICE-PROVIDER'
✅ SERVICE-PROVIDER
+ for value in "${values[@]}"
+ grep -qi error logs/excel/SERVICIOS-DIRECTOS.log
+ echo '✅ SERVICIOS-DIRECTOS'
✅ SERVICIOS-DIRECTOS
+ for value in "${values[@]}"
+ grep -qi error logs/excel/TRAZA-FEE.log
+ echo '✅ TRAZA-FEE'
✅ TRAZA-FEE
+ for value in "${values[@]}"
+ grep -qi error logs/excel/RETIROS.log
+ echo '❌ RETIROS (ver logs/excel/RETIROS.log)'
❌ RETIROS (ver logs/excel/RETIROS.log)
+ for value in "${values[@]}"
+ grep -qi error logs/excel/DEPOSITOS.log
+ echo '❌ DEPOSITOS (ver logs/excel/DEPOSITOS.log)'
❌ DEPOSITOS (ver logs/excel/DEPOSITOS.log)
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/32A.log
+ echo '✅ 32A'
✅ 32A
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/BCRP-NETO-EMISORES.log
+ echo '✅ BCRP-NETO-EMISORES'
✅ BCRP-NETO-EMISORES
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/CRANDES-PAGOS.log
+ echo '✅ CRANDES-PAGOS'
✅ CRANDES-PAGOS
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/VALIDAR-BALANCE.log
+ echo '❌ VALIDAR-BALANCE (ver logs/csv/VALIDAR-BALANCE.log)'
❌ VALIDAR-BALANCE (ver logs/csv/VALIDAR-BALANCE.log)
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/EQUIVALENCIA-LOG-TRX.log
+ echo '✅ EQUIVALENCIA-LOG-TRX'
✅ EQUIVALENCIA-LOG-TRX
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/EQUIVALENCIA-PAGOS.log
+ echo '✅ EQUIVALENCIA-PAGOS'
✅ EQUIVALENCIA-PAGOS
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/EQUIVALENCIA-AHORROS.log
+ echo '✅ EQUIVALENCIA-AHORROS'
✅ EQUIVALENCIA-AHORROS
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/BCRP-OPERACIONES-EMISOR.log
+ echo '✅ BCRP-OPERACIONES-EMISOR'
✅ BCRP-OPERACIONES-EMISOR
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/BCRP-TIPO-CUENTAS.log
+ echo '✅ BCRP-TIPO-CUENTAS'
✅ BCRP-TIPO-CUENTAS
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/AZULITO.log
+ echo '✅ AZULITO'
✅ AZULITO
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/UNIQUE.log
+ echo '✅ UNIQUE'
✅ UNIQUE
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/MOVISTAR.log
+ echo '✅ MOVISTAR'
✅ MOVISTAR
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/ENTEL.log
+ echo '✅ ENTEL'
✅ ENTEL
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/RETIRO-SENTINEL.log
+ echo '✅ RETIRO-SENTINEL'
✅ RETIRO-SENTINEL
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/RETIRO-WU-HUB.log
+ echo '✅ RETIRO-WU-HUB'
✅ RETIRO-WU-HUB
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/BCRP-BALANCES.log
+ echo '✅ BCRP-BALANCES'
✅ BCRP-BALANCES
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/QR-NIUBIZ.log
+ echo '❌ QR-NIUBIZ (ver logs/csv/QR-NIUBIZ.log)'
❌ QR-NIUBIZ (ver logs/csv/QR-NIUBIZ.log)
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/QR-IZIPAY.log
+ echo '❌ QR-IZIPAY (ver logs/csv/QR-IZIPAY.log)'
❌ QR-IZIPAY (ver logs/csv/QR-IZIPAY.log)
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/MTX-TRANSACTION.log
+ echo '✅ MTX-TRANSACTION'
✅ MTX-TRANSACTION
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/LOG-TRANSACCIONES.log
+ echo '✅ LOG-TRANSACCIONES'
✅ LOG-TRANSACCIONES
+ echo '== FIN DE EJECUCIÓN =='
== FIN DE EJECUCIÓN ==
Fin de la malla
Inicio de la malla
+ '[' -z 2025/06/12 ']'
+ fecha=2025/06/12
+ LOG_FILE=execution_status.log
+ MAX_TIME=1200
+ MAX_RETRIES=3
+ echo 2025/06/12
2025/06/12
+ cd /home/<USER>/generate/
+ mkdir -p logs/excel logs/csv logs/log_transacciones logs/reports32a_b logs/account_balances logs/log_usuarios logs/prepare logs/reporte_conciliacion logs/csv_to_pdf logs/prepare_rds logs/mysql_reports
+ echo '== Instalando dependencias si faltan =='
== Instalando dependencias si faltan ==
+ pip install -r requirements.txt
Requirement already satisfied: pdfkit==1.0.0 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 1)) (1.0.0)
Requirement already satisfied: pymysql==1.0.2 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 2)) (1.0.2)
Requirement already satisfied: openpyxl==3.1.5 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 3)) (3.1.5)
Requirement already satisfied: xlwt==1.3.0 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 4)) (1.3.0)
Requirement already satisfied: xlrd<2.0.0 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 5)) (1.2.0)
Requirement already satisfied: pandas==2.2.3 in /usr/local/lib64/python3.9/site-packages (from -r requirements.txt (line 6)) (2.2.3)
Requirement already satisfied: cryptography==44.0.2 in /usr/local/lib64/python3.9/site-packages (from -r requirements.txt (line 7)) (44.0.2)
Requirement already satisfied: boto3==1.37.16 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 8)) (1.37.16)
Requirement already satisfied: et-xmlfile in /usr/local/lib/python3.9/site-packages (from openpyxl==3.1.5->-r requirements.txt (line 3)) (2.0.0)
Requirement already satisfied: numpy>=1.22.4 in /usr/local/lib64/python3.9/site-packages (from pandas==2.2.3->-r requirements.txt (line 6)) (1.24.3)
Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.9/site-packages (from pandas==2.2.3->-r requirements.txt (line 6)) (2.8.2)
Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.9/site-packages (from pandas==2.2.3->-r requirements.txt (line 6)) (2025.1)
Requirement already satisfied: pytz>=2020.1 in /usr/lib/python3.9/site-packages (from pandas==2.2.3->-r requirements.txt (line 6)) (2022.7.1)
Requirement already satisfied: cffi>=1.12 in /usr/lib64/python3.9/site-packages (from cryptography==44.0.2->-r requirements.txt (line 7)) (1.14.5)
Requirement already satisfied: s3transfer<0.12.0,>=0.11.0 in /usr/local/lib/python3.9/site-packages (from boto3==1.37.16->-r requirements.txt (line 8)) (0.11.5)
Requirement already satisfied: jmespath<2.0.0,>=0.7.1 in /usr/lib/python3.9/site-packages (from boto3==1.37.16->-r requirements.txt (line 8)) (0.10.0)
Requirement already satisfied: botocore<1.38.0,>=1.37.16 in /usr/local/lib/python3.9/site-packages (from boto3==1.37.16->-r requirements.txt (line 8)) (1.37.38)
Requirement already satisfied: urllib3<1.27,>=1.25.4 in /usr/lib/python3.9/site-packages (from botocore<1.38.0,>=1.37.16->boto3==1.37.16->-r requirements.txt (line 8)) (1.25.10)
Requirement already satisfied: pycparser in /usr/lib/python3.9/site-packages (from cffi>=1.12->cryptography==44.0.2->-r requirements.txt (line 7)) (2.20)
Requirement already satisfied: six>=1.5 in /usr/lib/python3.9/site-packages (from python-dateutil>=2.8.2->pandas==2.2.3->-r requirements.txt (line 6)) (1.15.0)
Requirement already satisfied: ply==3.11 in /usr/lib/python3.9/site-packages (from pycparser->cffi>=1.12->cryptography==44.0.2->-r requirements.txt (line 7)) (3.11)
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
+ '[' '!' -f execution_status.log ']'
+ '[' '!' -s execution_status.log ']'
+ check_status_and_run POBLAR-NULL
+ PART_NAME=POBLAR-NULL
+ RETRY_COUNT=0
+ '[' 0 -lt 3 ']'
++ date
+ echo 'Fri Jun 13 05:05:02 UTC 2025 - Ejecutando POBLAR-NULL, intento #1'
Fri Jun 13 05:05:02 UTC 2025 - Ejecutando POBLAR-NULL, intento #1
++ date +%s
+ start_time=1749791102
+ pid=1506421
+ kill -0 1506421
+ python3 prepare/main.py 2025/06/12 POBLAR-NULL
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=85
+ '[' 85 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=90
+ '[' 90 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=95
+ '[' 95 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=100
+ '[' 100 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=105
+ '[' 105 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=110
+ '[' 110 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=115
+ '[' 115 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=120
+ '[' 120 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=125
+ '[' 125 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=130
+ '[' 130 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=135
+ '[' 135 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=140
+ '[' 140 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=145
+ '[' 145 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=150
+ '[' 150 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
++ date +%s
+ elapsed_time=155
+ '[' 155 -gt 1200 ']'
+ sleep 5
+ kill -0 1506421
+ wait 1506421
+ '[' 0 -eq 0 ']'
++ date
+ echo 'Fri Jun 13 05:07:42 UTC 2025 - POBLAR-NULL completado exitosamente'
Fri Jun 13 05:07:42 UTC 2025 - POBLAR-NULL completado exitosamente
+ return
+ check_status_and_run USER-MODIFY
+ PART_NAME=USER-MODIFY
+ RETRY_COUNT=0
+ '[' 0 -lt 3 ']'
++ date
+ echo 'Fri Jun 13 05:07:42 UTC 2025 - Ejecutando USER-MODIFY, intento #1'
Fri Jun 13 05:07:42 UTC 2025 - Ejecutando USER-MODIFY, intento #1
++ date +%s
+ start_time=1749791262
+ pid=1506766
+ kill -0 1506766
+ python3 prepare/main.py 2025/06/12 USER-MODIFY
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1200 ']'
+ sleep 5
+ kill -0 1506766
+ wait 1506766
+ '[' 0 -eq 0 ']'
++ date
+ echo 'Fri Jun 13 05:07:47 UTC 2025 - USER-MODIFY completado exitosamente'
Fri Jun 13 05:07:47 UTC 2025 - USER-MODIFY completado exitosamente
+ return
+ check_status_and_run BALANCE
+ PART_NAME=BALANCE
+ RETRY_COUNT=0
+ '[' 0 -lt 3 ']'
++ date
+ echo 'Fri Jun 13 05:07:47 UTC 2025 - Ejecutando BALANCE, intento #1'
Fri Jun 13 05:07:47 UTC 2025 - Ejecutando BALANCE, intento #1
++ date +%s
+ start_time=1749791267
+ pid=1506780
+ kill -0 1506780
+ python3 prepare/main.py 2025/06/12 BALANCE
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=85
+ '[' 85 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=90
+ '[' 90 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=95
+ '[' 95 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=100
+ '[' 100 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=105
+ '[' 105 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=110
+ '[' 110 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=115
+ '[' 115 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=121
+ '[' 121 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=126
+ '[' 126 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=131
+ '[' 131 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=136
+ '[' 136 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=141
+ '[' 141 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=146
+ '[' 146 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=151
+ '[' 151 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=156
+ '[' 156 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=161
+ '[' 161 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=166
+ '[' 166 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=171
+ '[' 171 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=176
+ '[' 176 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=181
+ '[' 181 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=186
+ '[' 186 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=191
+ '[' 191 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=196
+ '[' 196 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=201
+ '[' 201 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=206
+ '[' 206 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=211
+ '[' 211 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=216
+ '[' 216 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=221
+ '[' 221 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=226
+ '[' 226 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=231
+ '[' 231 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=236
+ '[' 236 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=241
+ '[' 241 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=246
+ '[' 246 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=251
+ '[' 251 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=256
+ '[' 256 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=261
+ '[' 261 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=266
+ '[' 266 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=271
+ '[' 271 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=276
+ '[' 276 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=281
+ '[' 281 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=286
+ '[' 286 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=291
+ '[' 291 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=296
+ '[' 296 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=301
+ '[' 301 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=306
+ '[' 306 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=311
+ '[' 311 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=316
+ '[' 316 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=321
+ '[' 321 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=326
+ '[' 326 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=331
+ '[' 331 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=336
+ '[' 336 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=341
+ '[' 341 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=346
+ '[' 346 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=351
+ '[' 351 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=356
+ '[' 356 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=361
+ '[' 361 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=366
+ '[' 366 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=371
+ '[' 371 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=376
+ '[' 376 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=381
+ '[' 381 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=386
+ '[' 386 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=391
+ '[' 391 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=396
+ '[' 396 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=401
+ '[' 401 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=406
+ '[' 406 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=411
+ '[' 411 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=416
+ '[' 416 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=421
+ '[' 421 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=426
+ '[' 426 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=431
+ '[' 431 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=436
+ '[' 436 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=441
+ '[' 441 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=446
+ '[' 446 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=451
+ '[' 451 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=456
+ '[' 456 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=461
+ '[' 461 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=466
+ '[' 466 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=471
+ '[' 471 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=476
+ '[' 476 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=481
+ '[' 481 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=486
+ '[' 486 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=491
+ '[' 491 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=496
+ '[' 496 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=501
+ '[' 501 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=506
+ '[' 506 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=511
+ '[' 511 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=516
+ '[' 516 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=521
+ '[' 521 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=526
+ '[' 526 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=531
+ '[' 531 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=536
+ '[' 536 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=541
+ '[' 541 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=546
+ '[' 546 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=551
+ '[' 551 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=556
+ '[' 556 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=561
+ '[' 561 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=566
+ '[' 566 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=571
+ '[' 571 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=576
+ '[' 576 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=581
+ '[' 581 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=586
+ '[' 586 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=591
+ '[' 591 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=596
+ '[' 596 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=601
+ '[' 601 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=606
+ '[' 606 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=611
+ '[' 611 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=616
+ '[' 616 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=621
+ '[' 621 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=626
+ '[' 626 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=631
+ '[' 631 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=636
+ '[' 636 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=641
+ '[' 641 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=646
+ '[' 646 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=651
+ '[' 651 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=656
+ '[' 656 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=661
+ '[' 661 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=666
+ '[' 666 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=671
+ '[' 671 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=676
+ '[' 676 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=681
+ '[' 681 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=686
+ '[' 686 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=691
+ '[' 691 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=696
+ '[' 696 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=701
+ '[' 701 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=706
+ '[' 706 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=711
+ '[' 711 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=716
+ '[' 716 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=721
+ '[' 721 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=726
+ '[' 726 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=731
+ '[' 731 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=736
+ '[' 736 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=741
+ '[' 741 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=746
+ '[' 746 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=751
+ '[' 751 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=756
+ '[' 756 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=761
+ '[' 761 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=766
+ '[' 766 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=771
+ '[' 771 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=776
+ '[' 776 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=781
+ '[' 781 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=786
+ '[' 786 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=791
+ '[' 791 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=796
+ '[' 796 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=801
+ '[' 801 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=806
+ '[' 806 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=811
+ '[' 811 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=816
+ '[' 816 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=821
+ '[' 821 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=826
+ '[' 826 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=831
+ '[' 831 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=836
+ '[' 836 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=841
+ '[' 841 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=846
+ '[' 846 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=851
+ '[' 851 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=856
+ '[' 856 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=861
+ '[' 861 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=866
+ '[' 866 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=871
+ '[' 871 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=876
+ '[' 876 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=881
+ '[' 881 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=886
+ '[' 886 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=891
+ '[' 891 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=896
+ '[' 896 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=901
+ '[' 901 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=906
+ '[' 906 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=911
+ '[' 911 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=916
+ '[' 916 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=921
+ '[' 921 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=926
+ '[' 926 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=931
+ '[' 931 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=936
+ '[' 936 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=941
+ '[' 941 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=946
+ '[' 946 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=951
+ '[' 951 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=956
+ '[' 956 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=961
+ '[' 961 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=966
+ '[' 966 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=971
+ '[' 971 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=976
+ '[' 976 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=981
+ '[' 981 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=986
+ '[' 986 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=991
+ '[' 991 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=996
+ '[' 996 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1001
+ '[' 1001 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1006
+ '[' 1006 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1011
+ '[' 1011 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1016
+ '[' 1016 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1021
+ '[' 1021 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1026
+ '[' 1026 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1031
+ '[' 1031 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1036
+ '[' 1036 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1041
+ '[' 1041 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1046
+ '[' 1046 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1051
+ '[' 1051 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1056
+ '[' 1056 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1061
+ '[' 1061 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1066
+ '[' 1066 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1071
+ '[' 1071 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1076
+ '[' 1076 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1081
+ '[' 1081 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1086
+ '[' 1086 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1091
+ '[' 1091 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1096
+ '[' 1096 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1101
+ '[' 1101 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1106
+ '[' 1106 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1111
+ '[' 1111 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1116
+ '[' 1116 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1121
+ '[' 1121 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1126
+ '[' 1126 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1131
+ '[' 1131 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1136
+ '[' 1136 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1141
+ '[' 1141 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1146
+ '[' 1146 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1151
+ '[' 1151 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1156
+ '[' 1156 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1161
+ '[' 1161 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1166
+ '[' 1166 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1171
+ '[' 1171 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1176
+ '[' 1176 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1181
+ '[' 1181 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1186
+ '[' 1186 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1191
+ '[' 1191 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1196
+ '[' 1196 -gt 1200 ']'
+ sleep 5
+ kill -0 1506780
++ date +%s
+ elapsed_time=1201
+ '[' 1201 -gt 1200 ']'
++ date
+ echo 'Fri Jun 13 05:27:48 UTC 2025 - El proceso BALANCE excedió el tiempo límite de 1200 segundos. Reiniciando...'
Fri Jun 13 05:27:48 UTC 2025 - El proceso BALANCE excedió el tiempo límite de 1200 segundos. Reiniciando...
+ kill -9 1506780
+ break
+ wait 1506780
/home/<USER>/generate/prepare.sh: line 52: 1506780 Killed                  python3 prepare/main.py "$fecha" "$PART_NAME" > "logs/prepare/$PART_NAME.log" 2>&1
+ '[' 137 -eq 0 ']'
++ date
+ echo 'Fri Jun 13 05:27:48 UTC 2025 - BALANCE falló.'
Fri Jun 13 05:27:48 UTC 2025 - BALANCE falló.
+ RETRY_COUNT=1
+ '[' 1 -lt 3 ']'
++ date
+ echo 'Fri Jun 13 05:27:48 UTC 2025 - Reintentando BALANCE...'
Fri Jun 13 05:27:48 UTC 2025 - Reintentando BALANCE...
+ '[' 1 -lt 3 ']'
++ date
+ echo 'Fri Jun 13 05:27:48 UTC 2025 - Ejecutando BALANCE, intento #2'
Fri Jun 13 05:27:48 UTC 2025 - Ejecutando BALANCE, intento #2
++ date +%s
+ start_time=1749792468
+ pid=1508806
+ kill -0 1508806
+ python3 prepare/main.py 2025/06/12 BALANCE
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1200 ']'
+ sleep 5
+ kill -0 1508806
+ wait 1508806
+ '[' 0 -eq 0 ']'
++ date
+ echo 'Fri Jun 13 05:29:03 UTC 2025 - BALANCE completado exitosamente'
Fri Jun 13 05:29:03 UTC 2025 - BALANCE completado exitosamente
+ return
+ check_status_and_run LOG-TRX
+ PART_NAME=LOG-TRX
+ RETRY_COUNT=0
+ '[' 0 -lt 3 ']'
++ date
+ echo 'Fri Jun 13 05:29:03 UTC 2025 - Ejecutando LOG-TRX, intento #1'
Fri Jun 13 05:29:03 UTC 2025 - Ejecutando LOG-TRX, intento #1
++ date +%s
+ start_time=1749792543
+ pid=1509145
+ kill -0 1509145
+ python3 prepare/main.py 2025/06/12 LOG-TRX
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=85
+ '[' 85 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=90
+ '[' 90 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=95
+ '[' 95 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=100
+ '[' 100 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=105
+ '[' 105 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=110
+ '[' 110 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=115
+ '[' 115 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=120
+ '[' 120 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=125
+ '[' 125 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=130
+ '[' 130 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=135
+ '[' 135 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=140
+ '[' 140 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=145
+ '[' 145 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=150
+ '[' 150 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=155
+ '[' 155 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=160
+ '[' 160 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=165
+ '[' 165 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=170
+ '[' 170 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=175
+ '[' 175 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=180
+ '[' 180 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=185
+ '[' 185 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=190
+ '[' 190 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=195
+ '[' 195 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=200
+ '[' 200 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=205
+ '[' 205 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=210
+ '[' 210 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=215
+ '[' 215 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=220
+ '[' 220 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=225
+ '[' 225 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=230
+ '[' 230 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=235
+ '[' 235 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=240
+ '[' 240 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=245
+ '[' 245 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=250
+ '[' 250 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=255
+ '[' 255 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=260
+ '[' 260 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=265
+ '[' 265 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=270
+ '[' 270 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=275
+ '[' 275 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=280
+ '[' 280 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=285
+ '[' 285 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=290
+ '[' 290 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=295
+ '[' 295 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=300
+ '[' 300 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=305
+ '[' 305 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=310
+ '[' 310 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=315
+ '[' 315 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=320
+ '[' 320 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=325
+ '[' 325 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=330
+ '[' 330 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=335
+ '[' 335 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=340
+ '[' 340 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=345
+ '[' 345 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=350
+ '[' 350 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=355
+ '[' 355 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=360
+ '[' 360 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=365
+ '[' 365 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=370
+ '[' 370 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=375
+ '[' 375 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=380
+ '[' 380 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=385
+ '[' 385 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=390
+ '[' 390 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=395
+ '[' 395 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=400
+ '[' 400 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=405
+ '[' 405 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=410
+ '[' 410 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=415
+ '[' 415 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=420
+ '[' 420 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=425
+ '[' 425 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=430
+ '[' 430 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=435
+ '[' 435 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=440
+ '[' 440 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=445
+ '[' 445 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=450
+ '[' 450 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=455
+ '[' 455 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=460
+ '[' 460 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=465
+ '[' 465 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=470
+ '[' 470 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=475
+ '[' 475 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=480
+ '[' 480 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=485
+ '[' 485 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=490
+ '[' 490 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=495
+ '[' 495 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=500
+ '[' 500 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=505
+ '[' 505 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=510
+ '[' 510 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=515
+ '[' 515 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=520
+ '[' 520 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=525
+ '[' 525 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=530
+ '[' 530 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=535
+ '[' 535 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=540
+ '[' 540 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=545
+ '[' 545 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=550
+ '[' 550 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=555
+ '[' 555 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=560
+ '[' 560 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=565
+ '[' 565 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=570
+ '[' 570 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=575
+ '[' 575 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=580
+ '[' 580 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=585
+ '[' 585 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=590
+ '[' 590 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=595
+ '[' 595 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=600
+ '[' 600 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=605
+ '[' 605 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=611
+ '[' 611 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=616
+ '[' 616 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=621
+ '[' 621 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=626
+ '[' 626 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=631
+ '[' 631 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=636
+ '[' 636 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=641
+ '[' 641 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=646
+ '[' 646 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=651
+ '[' 651 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=656
+ '[' 656 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=661
+ '[' 661 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=666
+ '[' 666 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=671
+ '[' 671 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=676
+ '[' 676 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=681
+ '[' 681 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=686
+ '[' 686 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=691
+ '[' 691 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=696
+ '[' 696 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=701
+ '[' 701 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=706
+ '[' 706 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=711
+ '[' 711 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=716
+ '[' 716 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=721
+ '[' 721 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=726
+ '[' 726 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=731
+ '[' 731 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=736
+ '[' 736 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=741
+ '[' 741 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=746
+ '[' 746 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=751
+ '[' 751 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=756
+ '[' 756 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=761
+ '[' 761 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=766
+ '[' 766 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=771
+ '[' 771 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=776
+ '[' 776 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=781
+ '[' 781 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=786
+ '[' 786 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=791
+ '[' 791 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=796
+ '[' 796 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=801
+ '[' 801 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=806
+ '[' 806 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=811
+ '[' 811 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=816
+ '[' 816 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=821
+ '[' 821 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=826
+ '[' 826 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=831
+ '[' 831 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=836
+ '[' 836 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=841
+ '[' 841 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=846
+ '[' 846 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=851
+ '[' 851 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=856
+ '[' 856 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=861
+ '[' 861 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=866
+ '[' 866 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=871
+ '[' 871 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=876
+ '[' 876 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=881
+ '[' 881 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=886
+ '[' 886 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=891
+ '[' 891 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=896
+ '[' 896 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=901
+ '[' 901 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=906
+ '[' 906 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=911
+ '[' 911 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=916
+ '[' 916 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=921
+ '[' 921 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=926
+ '[' 926 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=931
+ '[' 931 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=936
+ '[' 936 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=941
+ '[' 941 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=946
+ '[' 946 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=951
+ '[' 951 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=956
+ '[' 956 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=961
+ '[' 961 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=966
+ '[' 966 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=971
+ '[' 971 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=976
+ '[' 976 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=981
+ '[' 981 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=986
+ '[' 986 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=991
+ '[' 991 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=996
+ '[' 996 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1001
+ '[' 1001 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1006
+ '[' 1006 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1011
+ '[' 1011 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1016
+ '[' 1016 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1021
+ '[' 1021 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1026
+ '[' 1026 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1031
+ '[' 1031 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1036
+ '[' 1036 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1041
+ '[' 1041 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1046
+ '[' 1046 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1051
+ '[' 1051 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1056
+ '[' 1056 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1061
+ '[' 1061 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1066
+ '[' 1066 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1071
+ '[' 1071 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1076
+ '[' 1076 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1081
+ '[' 1081 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1086
+ '[' 1086 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1091
+ '[' 1091 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1096
+ '[' 1096 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1101
+ '[' 1101 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1106
+ '[' 1106 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1111
+ '[' 1111 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1116
+ '[' 1116 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1121
+ '[' 1121 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1126
+ '[' 1126 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1131
+ '[' 1131 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1136
+ '[' 1136 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1141
+ '[' 1141 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1146
+ '[' 1146 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1151
+ '[' 1151 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1156
+ '[' 1156 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1161
+ '[' 1161 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1166
+ '[' 1166 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1171
+ '[' 1171 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1176
+ '[' 1176 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1181
+ '[' 1181 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1186
+ '[' 1186 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1191
+ '[' 1191 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1196
+ '[' 1196 -gt 1200 ']'
+ sleep 5
+ kill -0 1509145
++ date +%s
+ elapsed_time=1201
+ '[' 1201 -gt 1200 ']'
++ date
+ echo 'Fri Jun 13 05:49:04 UTC 2025 - El proceso LOG-TRX excedió el tiempo límite de 1200 segundos. Reiniciando...'
Fri Jun 13 05:49:04 UTC 2025 - El proceso LOG-TRX excedió el tiempo límite de 1200 segundos. Reiniciando...
+ kill -9 1509145
+ break
+ wait 1509145
/home/<USER>/generate/prepare.sh: line 52: 1509145 Killed                  python3 prepare/main.py "$fecha" "$PART_NAME" > "logs/prepare/$PART_NAME.log" 2>&1
+ '[' 137 -eq 0 ']'
++ date
+ echo 'Fri Jun 13 05:49:04 UTC 2025 - LOG-TRX falló.'
Fri Jun 13 05:49:04 UTC 2025 - LOG-TRX falló.
+ RETRY_COUNT=1
+ '[' 1 -lt 3 ']'
++ date
+ echo 'Fri Jun 13 05:49:04 UTC 2025 - Reintentando LOG-TRX...'
Fri Jun 13 05:49:04 UTC 2025 - Reintentando LOG-TRX...
+ '[' 1 -lt 3 ']'
++ date
+ echo 'Fri Jun 13 05:49:04 UTC 2025 - Ejecutando LOG-TRX, intento #2'
Fri Jun 13 05:49:04 UTC 2025 - Ejecutando LOG-TRX, intento #2
++ date +%s
+ start_time=1749793744
+ pid=1511654
+ kill -0 1511654
+ python3 prepare/main.py 2025/06/12 LOG-TRX
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=85
+ '[' 85 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=90
+ '[' 90 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=95
+ '[' 95 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=100
+ '[' 100 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=105
+ '[' 105 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=110
+ '[' 110 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=115
+ '[' 115 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=120
+ '[' 120 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=125
+ '[' 125 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=130
+ '[' 130 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=135
+ '[' 135 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=140
+ '[' 140 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=145
+ '[' 145 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=150
+ '[' 150 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=155
+ '[' 155 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=160
+ '[' 160 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=165
+ '[' 165 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=170
+ '[' 170 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=175
+ '[' 175 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=180
+ '[' 180 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=185
+ '[' 185 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=190
+ '[' 190 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=195
+ '[' 195 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=200
+ '[' 200 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=205
+ '[' 205 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=210
+ '[' 210 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=215
+ '[' 215 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=220
+ '[' 220 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=225
+ '[' 225 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=230
+ '[' 230 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=235
+ '[' 235 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=240
+ '[' 240 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=245
+ '[' 245 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=250
+ '[' 250 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=255
+ '[' 255 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
++ date +%s
+ elapsed_time=260
+ '[' 260 -gt 1200 ']'
+ sleep 5
+ kill -0 1511654
+ wait 1511654
+ '[' 0 -eq 0 ']'
++ date
+ echo 'Fri Jun 13 05:53:29 UTC 2025 - LOG-TRX completado exitosamente'
Fri Jun 13 05:53:29 UTC 2025 - LOG-TRX completado exitosamente
+ return
+ grep -q 'PROCESO COMPLETADO' execution_status.log
+ echo 2025/06/12 '- PROCESO COMPLETADO'
+ echo 'Proceso finalizado. Revisa el archivo de log: execution_status.log'
Proceso finalizado. Revisa el archivo de log: execution_status.log
+ '[' -z 2025/06/12 ']'
+ fecha=2025/06/12
+ LOG_FILE=execution_status.log
+ MAX_TIME=1500
+ MAX_RETRIES=3
+ echo 2025/06/12
2025/06/12
+ cd /home/<USER>/generate/
+ echo 'Proceso previo iniciado.'
Proceso previo iniciado.
+ check_status_and_run PRE-LOG-USR
+ PART_NAME=PRE-LOG-USR
+ RETRY_COUNT=0
+ '[' 0 -lt 3 ']'
++ date
+ echo 'Fri Jun 13 05:53:29 UTC 2025 - Ejecutando PRE-LOG-USR, intento #1'
Fri Jun 13 05:53:29 UTC 2025 - Ejecutando PRE-LOG-USR, intento #1
++ date +%s
+ start_time=1749794009
+ pid=1512312
+ kill -0 1512312
+ python3 prepare/main.py 2025/06/12 PRE-LOG-USR
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1500 ']'
+ sleep 5
+ kill -0 1512312
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1500 ']'
+ sleep 5
+ kill -0 1512312
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1500 ']'
+ sleep 5
+ kill -0 1512312
+ wait 1512312
+ '[' 0 -eq 0 ']'
++ date
+ echo 'Fri Jun 13 05:53:44 UTC 2025 - PRE-LOG-USR completado exitosamente'
Fri Jun 13 05:53:44 UTC 2025 - PRE-LOG-USR completado exitosamente
+ return
+ echo 'Proceso previo finalizado.'
Proceso previo finalizado.
+ echo 'Proceso LOG-USR inicidado.'
Proceso LOG-USR inicidado.
+ check_status_and_run LOG-USR
+ PART_NAME=LOG-USR
+ RETRY_COUNT=0
+ '[' 0 -lt 3 ']'
++ date
+ echo 'Fri Jun 13 05:53:44 UTC 2025 - Ejecutando LOG-USR, intento #1'
Fri Jun 13 05:53:44 UTC 2025 - Ejecutando LOG-USR, intento #1
++ date +%s
+ start_time=1749794024
+ pid=1512330
+ kill -0 1512330
+ python3 prepare/main.py 2025/06/12 LOG-USR
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1500 ']'
+ sleep 5
+ kill -0 1512330
+ wait 1512330
+ '[' 0 -eq 0 ']'
++ date
+ echo 'Fri Jun 13 05:54:44 UTC 2025 - LOG-USR completado exitosamente'
Fri Jun 13 05:54:44 UTC 2025 - LOG-USR completado exitosamente
+ return
+ echo 'Proceso LOG-USR finalizado.'
Proceso LOG-USR finalizado.
+ echo 'Generando log de usuarios'
Generando log de usuarios
+ sh -x /home/<USER>/generate/log_user.sh 2025/06/12
+ export PRIVATE_KEY_PATH=/home/<USER>/generate/FileSigner/private_key.key
+ PRIVATE_KEY_PATH=/home/<USER>/generate/FileSigner/private_key.key
+ export PRIVATE_CRT_PATH=/home/<USER>/generate/FileSigner/private_key.crt
+ PRIVATE_CRT_PATH=/home/<USER>/generate/FileSigner/private_key.crt
+ export OUTPUT_ROUTE=/home/<USER>/output/excel/
+ OUTPUT_ROUTE=/home/<USER>/output/excel/
+ export OUTPUT_ROUTE_CSV=/home/<USER>/output/csv/
+ OUTPUT_ROUTE_CSV=/home/<USER>/output/csv/
+ valuescsv=("LOG-USUARIOS")
+ export REPORTS_NO_S3=LOG-USUARIOS
+ REPORTS_NO_S3=LOG-USUARIOS
+ ROUTE_CSV=/home/<USER>/output/csv
+ '[' -z 2025/06/12 ']'
+ fecha=2025/06/12
++ date -d '2025/06/12 + 1 day' +%Y%m%d
+ fecha_path=********
+ cd /home/<USER>/generate/
+ for value in "${valuescsv[@]}"
+ wait
+ python3 exports_csv/main.py LOG-USUARIOS 2025/06/12
+ cd /home/<USER>/generate/log_usuarios/
+ wait
+ python3 procesar.py 2025/06/12
+ export PRIVATE_KEY_PATH=/home/<USER>/generate/FileSigner/pdp_sign.key
+ PRIVATE_KEY_PATH=/home/<USER>/generate/FileSigner/pdp_sign.key
+ export PRIVATE_CRT_PATH=/home/<USER>/generate/FileSigner/SignFileNC.crt
+ PRIVATE_CRT_PATH=/home/<USER>/generate/FileSigner/SignFileNC.crt
+ export OUTPUT_ROUTE=/home/<USER>/output/excel/
+ OUTPUT_ROUTE=/home/<USER>/output/excel/
+ export OUTPUT_ROUTE_CSV=/home/<USER>/output/csv/
+ OUTPUT_ROUTE_CSV=/home/<USER>/output/csv/
+ values32=("32A")
+ values=("BITEL-POST" "BITEL-PRE" "SERVICE-PROVIDER" "SERVICIOS-DIRECTOS" "TRAZA-FEE" "RETIROS" "DEPOSITOS")
+ valuescsv=("32A" "BCRP-NETO-EMISORES" "CRANDES-PAGOS" "VALIDAR-BALANCE" "EQUIVALENCIA-LOG-TRX" "EQUIVALENCIA-PAGOS" "EQUIVALENCIA-AHORROS" "BCRP-OPERACIONES-EMISOR" "BCRP-TIPO-CUENTAS" "AZULITO" "UNIQUE" "MOVISTAR" "ENTEL" "RETIRO-SENTINEL" "RETIRO-WU-HUB" "BCRP-BALANCES" "QR-NIUBIZ" "QR-IZIPAY" "MTX-TRANSACTION" "LOG-TRANSACCIONES")
+ export REPORTS_NO_S3=LOG-TRANSACCIONES,MTX-TRANSACTION,USER-BALANCES,32A
+ REPORTS_NO_S3=LOG-TRANSACCIONES,MTX-TRANSACTION,USER-BALANCES,32A
+ MAX_TIME=1800
+ ROUTE_CSV=/home/<USER>/output/csv
+ TARGET_PATH=/home/<USER>/output/load_rds
+ '[' -z 2025/06/12 ']'
+ fecha=2025/06/12
++ date -d '2025/06/12 + 1 day' +%Y%m%d
+ fecha_path=********
+ cd /home/<USER>/generate/
+ mkdir -p logs/excel logs/csv logs/log_transacciones logs/reports32a_b logs/account_balances logs/interope logs/log_usuarios logs/prepare logs/reporte_conciliacion logs/csv_to_pdf logs/prepare_rds logs/mysql_reports
+ echo '== Generando REPORTES EXCEL =='
== Generando REPORTES EXCEL ==
+ for value in "${values[@]}"
+ run_with_timeout BITEL-POST exports_excel/main.py BITEL-POST 2025/06/12 logs/excel/BITEL-POST.log
+ PROCESS_NAME=BITEL-POST
+ SCRIPT=exports_excel/main.py
+ REPORT=BITEL-POST
+ DATE=2025/06/12
+ LOG_FILE=logs/excel/BITEL-POST.log
+ echo 'Iniciando: BITEL-POST'
Iniciando: BITEL-POST
++ date +%s
+ start_time=**********
+ pid=1513273
+ kill -0 1513273
+ python3 exports_excel/main.py BITEL-POST 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=51
+ '[' 51 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=56
+ '[' 56 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=61
+ '[' 61 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=66
+ '[' 66 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=71
+ '[' 71 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=76
+ '[' 76 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=81
+ '[' 81 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=86
+ '[' 86 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=91
+ '[' 91 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=96
+ '[' 96 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=101
+ '[' 101 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=106
+ '[' 106 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=111
+ '[' 111 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=116
+ '[' 116 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=121
+ '[' 121 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=126
+ '[' 126 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=131
+ '[' 131 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=136
+ '[' 136 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=141
+ '[' 141 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=146
+ '[' 146 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=151
+ '[' 151 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=156
+ '[' 156 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=161
+ '[' 161 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=166
+ '[' 166 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=171
+ '[' 171 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=176
+ '[' 176 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=181
+ '[' 181 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=186
+ '[' 186 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=191
+ '[' 191 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=196
+ '[' 196 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=201
+ '[' 201 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=206
+ '[' 206 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=211
+ '[' 211 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=216
+ '[' 216 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=221
+ '[' 221 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=226
+ '[' 226 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=231
+ '[' 231 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=236
+ '[' 236 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=241
+ '[' 241 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=246
+ '[' 246 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=251
+ '[' 251 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=256
+ '[' 256 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=261
+ '[' 261 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=266
+ '[' 266 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=271
+ '[' 271 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=276
+ '[' 276 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=281
+ '[' 281 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=286
+ '[' 286 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=291
+ '[' 291 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=296
+ '[' 296 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=301
+ '[' 301 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=306
+ '[' 306 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=311
+ '[' 311 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=316
+ '[' 316 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=321
+ '[' 321 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=326
+ '[' 326 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=331
+ '[' 331 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=336
+ '[' 336 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=341
+ '[' 341 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=346
+ '[' 346 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=351
+ '[' 351 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=356
+ '[' 356 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=361
+ '[' 361 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=366
+ '[' 366 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=371
+ '[' 371 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=376
+ '[' 376 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=381
+ '[' 381 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=386
+ '[' 386 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=391
+ '[' 391 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=396
+ '[' 396 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=401
+ '[' 401 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=406
+ '[' 406 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=411
+ '[' 411 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=416
+ '[' 416 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=421
+ '[' 421 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=426
+ '[' 426 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=431
+ '[' 431 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=436
+ '[' 436 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=441
+ '[' 441 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=446
+ '[' 446 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=451
+ '[' 451 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=456
+ '[' 456 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=461
+ '[' 461 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=466
+ '[' 466 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=471
+ '[' 471 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=476
+ '[' 476 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=481
+ '[' 481 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=486
+ '[' 486 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=491
+ '[' 491 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=496
+ '[' 496 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=501
+ '[' 501 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=506
+ '[' 506 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=511
+ '[' 511 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=516
+ '[' 516 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=521
+ '[' 521 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=526
+ '[' 526 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=531
+ '[' 531 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=536
+ '[' 536 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=541
+ '[' 541 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=546
+ '[' 546 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=551
+ '[' 551 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=556
+ '[' 556 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=561
+ '[' 561 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=566
+ '[' 566 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=571
+ '[' 571 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=576
+ '[' 576 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=581
+ '[' 581 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=586
+ '[' 586 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=591
+ '[' 591 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=596
+ '[' 596 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=601
+ '[' 601 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=606
+ '[' 606 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=611
+ '[' 611 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=616
+ '[' 616 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=621
+ '[' 621 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=626
+ '[' 626 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=631
+ '[' 631 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=636
+ '[' 636 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=641
+ '[' 641 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=646
+ '[' 646 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=651
+ '[' 651 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=656
+ '[' 656 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=661
+ '[' 661 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=666
+ '[' 666 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=671
+ '[' 671 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=676
+ '[' 676 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=681
+ '[' 681 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=686
+ '[' 686 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=691
+ '[' 691 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=696
+ '[' 696 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=701
+ '[' 701 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=706
+ '[' 706 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=711
+ '[' 711 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=716
+ '[' 716 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=721
+ '[' 721 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=726
+ '[' 726 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=731
+ '[' 731 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=736
+ '[' 736 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=741
+ '[' 741 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=746
+ '[' 746 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=751
+ '[' 751 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=756
+ '[' 756 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=761
+ '[' 761 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=766
+ '[' 766 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=771
+ '[' 771 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=776
+ '[' 776 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=781
+ '[' 781 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=786
+ '[' 786 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=791
+ '[' 791 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=796
+ '[' 796 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=801
+ '[' 801 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=806
+ '[' 806 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=811
+ '[' 811 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=816
+ '[' 816 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=821
+ '[' 821 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=826
+ '[' 826 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=831
+ '[' 831 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=836
+ '[' 836 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=841
+ '[' 841 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=846
+ '[' 846 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=851
+ '[' 851 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=856
+ '[' 856 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=861
+ '[' 861 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=866
+ '[' 866 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=871
+ '[' 871 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=876
+ '[' 876 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=881
+ '[' 881 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=886
+ '[' 886 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=891
+ '[' 891 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=896
+ '[' 896 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=901
+ '[' 901 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=906
+ '[' 906 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=911
+ '[' 911 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=916
+ '[' 916 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=921
+ '[' 921 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=926
+ '[' 926 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=931
+ '[' 931 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=936
+ '[' 936 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=941
+ '[' 941 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=946
+ '[' 946 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=951
+ '[' 951 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=956
+ '[' 956 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=961
+ '[' 961 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=966
+ '[' 966 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=971
+ '[' 971 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=976
+ '[' 976 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=981
+ '[' 981 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=986
+ '[' 986 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=991
+ '[' 991 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=996
+ '[' 996 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1001
+ '[' 1001 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1006
+ '[' 1006 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1011
+ '[' 1011 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1016
+ '[' 1016 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1021
+ '[' 1021 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1026
+ '[' 1026 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1031
+ '[' 1031 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1036
+ '[' 1036 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1041
+ '[' 1041 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1046
+ '[' 1046 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1051
+ '[' 1051 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1056
+ '[' 1056 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1061
+ '[' 1061 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1066
+ '[' 1066 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1071
+ '[' 1071 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1076
+ '[' 1076 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1081
+ '[' 1081 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1086
+ '[' 1086 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1091
+ '[' 1091 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1096
+ '[' 1096 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1101
+ '[' 1101 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1106
+ '[' 1106 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1111
+ '[' 1111 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1116
+ '[' 1116 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1121
+ '[' 1121 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1126
+ '[' 1126 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1131
+ '[' 1131 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1136
+ '[' 1136 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1141
+ '[' 1141 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1146
+ '[' 1146 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1151
+ '[' 1151 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1156
+ '[' 1156 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1161
+ '[' 1161 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1166
+ '[' 1166 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1171
+ '[' 1171 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1176
+ '[' 1176 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1181
+ '[' 1181 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1186
+ '[' 1186 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1191
+ '[' 1191 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1196
+ '[' 1196 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1201
+ '[' 1201 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1206
+ '[' 1206 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1211
+ '[' 1211 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1216
+ '[' 1216 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1221
+ '[' 1221 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1226
+ '[' 1226 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1231
+ '[' 1231 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1236
+ '[' 1236 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1241
+ '[' 1241 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1246
+ '[' 1246 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1251
+ '[' 1251 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1256
+ '[' 1256 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1261
+ '[' 1261 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1266
+ '[' 1266 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1271
+ '[' 1271 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1276
+ '[' 1276 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1281
+ '[' 1281 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1286
+ '[' 1286 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1291
+ '[' 1291 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1296
+ '[' 1296 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1301
+ '[' 1301 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1306
+ '[' 1306 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1311
+ '[' 1311 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1316
+ '[' 1316 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1321
+ '[' 1321 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1326
+ '[' 1326 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1331
+ '[' 1331 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1336
+ '[' 1336 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1341
+ '[' 1341 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1346
+ '[' 1346 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1351
+ '[' 1351 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1356
+ '[' 1356 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1361
+ '[' 1361 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1366
+ '[' 1366 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1371
+ '[' 1371 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1376
+ '[' 1376 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1381
+ '[' 1381 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1386
+ '[' 1386 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1391
+ '[' 1391 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1396
+ '[' 1396 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1401
+ '[' 1401 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1406
+ '[' 1406 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1411
+ '[' 1411 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1416
+ '[' 1416 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1421
+ '[' 1421 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1426
+ '[' 1426 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1431
+ '[' 1431 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1436
+ '[' 1436 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1441
+ '[' 1441 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1446
+ '[' 1446 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1451
+ '[' 1451 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1456
+ '[' 1456 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1461
+ '[' 1461 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1466
+ '[' 1466 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1471
+ '[' 1471 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1476
+ '[' 1476 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1481
+ '[' 1481 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1486
+ '[' 1486 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1491
+ '[' 1491 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1496
+ '[' 1496 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1501
+ '[' 1501 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1506
+ '[' 1506 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1511
+ '[' 1511 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1516
+ '[' 1516 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1521
+ '[' 1521 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1526
+ '[' 1526 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1531
+ '[' 1531 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1536
+ '[' 1536 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1541
+ '[' 1541 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1546
+ '[' 1546 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1551
+ '[' 1551 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1556
+ '[' 1556 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1561
+ '[' 1561 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1566
+ '[' 1566 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1571
+ '[' 1571 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1576
+ '[' 1576 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1581
+ '[' 1581 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1586
+ '[' 1586 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1591
+ '[' 1591 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1596
+ '[' 1596 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1601
+ '[' 1601 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1606
+ '[' 1606 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1611
+ '[' 1611 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1616
+ '[' 1616 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1621
+ '[' 1621 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1626
+ '[' 1626 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1631
+ '[' 1631 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1636
+ '[' 1636 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1641
+ '[' 1641 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1646
+ '[' 1646 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1651
+ '[' 1651 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1656
+ '[' 1656 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1661
+ '[' 1661 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1666
+ '[' 1666 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1671
+ '[' 1671 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1676
+ '[' 1676 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1681
+ '[' 1681 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1686
+ '[' 1686 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1691
+ '[' 1691 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1696
+ '[' 1696 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1701
+ '[' 1701 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1706
+ '[' 1706 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1711
+ '[' 1711 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1716
+ '[' 1716 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1721
+ '[' 1721 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1726
+ '[' 1726 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1731
+ '[' 1731 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1736
+ '[' 1736 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1741
+ '[' 1741 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1746
+ '[' 1746 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1751
+ '[' 1751 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1756
+ '[' 1756 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1761
+ '[' 1761 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1766
+ '[' 1766 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1771
+ '[' 1771 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1776
+ '[' 1776 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1781
+ '[' 1781 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1786
+ '[' 1786 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1791
+ '[' 1791 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1796
+ '[' 1796 -gt 1800 ']'
+ sleep 5
+ kill -0 1513273
++ date +%s
+ elapsed_time=1801
+ '[' 1801 -gt 1800 ']'
+ echo 'El proceso BITEL-POST excedió el tiempo límite de 1800 segundos. Terminando...'
El proceso BITEL-POST excedió el tiempo límite de 1800 segundos. Terminando...
+ kill -9 1513273
+ echo 'Proceso terminado por timeout después de 1801 segundos'
+ break
+ wait 1513273
/home/<USER>/generate/ejecuta_diario.sh: line 48: 1513273 Killed                  python3 $SCRIPT $REPORT $DATE > "$LOG_FILE" 2>&1
+ return_code=137
+ '[' 137 -eq 0 ']'
+ echo '❌ BITEL-POST falló (código: 137)'
❌ BITEL-POST falló (código: 137)
+ for value in "${values[@]}"
+ run_with_timeout BITEL-PRE exports_excel/main.py BITEL-PRE 2025/06/12 logs/excel/BITEL-PRE.log
+ PROCESS_NAME=BITEL-PRE
+ SCRIPT=exports_excel/main.py
+ REPORT=BITEL-PRE
+ DATE=2025/06/12
+ LOG_FILE=logs/excel/BITEL-PRE.log
+ echo 'Iniciando: BITEL-PRE'
Iniciando: BITEL-PRE
++ date +%s
+ start_time=1749796164
+ pid=1518227
+ kill -0 1518227
+ python3 exports_excel/main.py BITEL-PRE 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=71
+ '[' 71 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=76
+ '[' 76 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=81
+ '[' 81 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=86
+ '[' 86 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=91
+ '[' 91 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=96
+ '[' 96 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=101
+ '[' 101 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=106
+ '[' 106 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=111
+ '[' 111 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=116
+ '[' 116 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=121
+ '[' 121 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=126
+ '[' 126 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=131
+ '[' 131 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=136
+ '[' 136 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=141
+ '[' 141 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=146
+ '[' 146 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=151
+ '[' 151 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=156
+ '[' 156 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=161
+ '[' 161 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=166
+ '[' 166 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=171
+ '[' 171 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=176
+ '[' 176 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=181
+ '[' 181 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=186
+ '[' 186 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=191
+ '[' 191 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=196
+ '[' 196 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=201
+ '[' 201 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=206
+ '[' 206 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=211
+ '[' 211 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=216
+ '[' 216 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=221
+ '[' 221 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=226
+ '[' 226 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=231
+ '[' 231 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=236
+ '[' 236 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=241
+ '[' 241 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=246
+ '[' 246 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=251
+ '[' 251 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=256
+ '[' 256 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=261
+ '[' 261 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=266
+ '[' 266 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=271
+ '[' 271 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=276
+ '[' 276 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=281
+ '[' 281 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=286
+ '[' 286 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=291
+ '[' 291 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=296
+ '[' 296 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=301
+ '[' 301 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=306
+ '[' 306 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=311
+ '[' 311 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=316
+ '[' 316 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=321
+ '[' 321 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=326
+ '[' 326 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=331
+ '[' 331 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=336
+ '[' 336 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
++ date +%s
+ elapsed_time=341
+ '[' 341 -gt 1800 ']'
+ sleep 5
+ kill -0 1518227
+ wait 1518227
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ BITEL-PRE completado exitosamente'
✅ BITEL-PRE completado exitosamente
+ for value in "${values[@]}"
+ run_with_timeout SERVICE-PROVIDER exports_excel/main.py SERVICE-PROVIDER 2025/06/12 logs/excel/SERVICE-PROVIDER.log
+ PROCESS_NAME=SERVICE-PROVIDER
+ SCRIPT=exports_excel/main.py
+ REPORT=SERVICE-PROVIDER
+ DATE=2025/06/12
+ LOG_FILE=logs/excel/SERVICE-PROVIDER.log
+ echo 'Iniciando: SERVICE-PROVIDER'
Iniciando: SERVICE-PROVIDER
++ date +%s
+ start_time=**********
+ pid=1519386
+ kill -0 1519386
+ python3 exports_excel/main.py SERVICE-PROVIDER 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1519386
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1519386
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1519386
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1519386
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 1519386
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 1519386
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 1519386
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 1519386
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 1519386
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 1519386
+ wait 1519386
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ SERVICE-PROVIDER completado exitosamente'
✅ SERVICE-PROVIDER completado exitosamente
+ for value in "${values[@]}"
+ run_with_timeout SERVICIOS-DIRECTOS exports_excel/main.py SERVICIOS-DIRECTOS 2025/06/12 logs/excel/SERVICIOS-DIRECTOS.log
+ PROCESS_NAME=SERVICIOS-DIRECTOS
+ SCRIPT=exports_excel/main.py
+ REPORT=SERVICIOS-DIRECTOS
+ DATE=2025/06/12
+ LOG_FILE=logs/excel/SERVICIOS-DIRECTOS.log
+ echo 'Iniciando: SERVICIOS-DIRECTOS'
Iniciando: SERVICIOS-DIRECTOS
++ date +%s
+ start_time=**********
+ pid=1519519
+ kill -0 1519519
+ python3 exports_excel/main.py SERVICIOS-DIRECTOS 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1519519
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1519519
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1519519
+ wait 1519519
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ SERVICIOS-DIRECTOS completado exitosamente'
✅ SERVICIOS-DIRECTOS completado exitosamente
+ for value in "${values[@]}"
+ run_with_timeout TRAZA-FEE exports_excel/main.py TRAZA-FEE 2025/06/12 logs/excel/TRAZA-FEE.log
+ PROCESS_NAME=TRAZA-FEE
+ SCRIPT=exports_excel/main.py
+ REPORT=TRAZA-FEE
+ DATE=2025/06/12
+ LOG_FILE=logs/excel/TRAZA-FEE.log
+ echo 'Iniciando: TRAZA-FEE'
Iniciando: TRAZA-FEE
++ date +%s
+ start_time=1749796575
+ pid=1519537
+ kill -0 1519537
+ python3 exports_excel/main.py TRAZA-FEE 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1519537
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1519537
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1519537
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1519537
+ wait 1519537
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ TRAZA-FEE completado exitosamente'
✅ TRAZA-FEE completado exitosamente
+ for value in "${values[@]}"
+ run_with_timeout RETIROS exports_excel/main.py RETIROS 2025/06/12 logs/excel/RETIROS.log
+ PROCESS_NAME=RETIROS
+ SCRIPT=exports_excel/main.py
+ REPORT=RETIROS
+ DATE=2025/06/12
+ LOG_FILE=logs/excel/RETIROS.log
+ echo 'Iniciando: RETIROS'
Iniciando: RETIROS
++ date +%s
+ start_time=1749796595
+ pid=1519581
+ kill -0 1519581
+ python3 exports_excel/main.py RETIROS 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1519581
+ wait 1519581
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ RETIROS completado exitosamente'
✅ RETIROS completado exitosamente
+ for value in "${values[@]}"
+ run_with_timeout DEPOSITOS exports_excel/main.py DEPOSITOS 2025/06/12 logs/excel/DEPOSITOS.log
+ PROCESS_NAME=DEPOSITOS
+ SCRIPT=exports_excel/main.py
+ REPORT=DEPOSITOS
+ DATE=2025/06/12
+ LOG_FILE=logs/excel/DEPOSITOS.log
+ echo 'Iniciando: DEPOSITOS'
Iniciando: DEPOSITOS
++ date +%s
+ start_time=1749796600
+ pid=1519621
+ kill -0 1519621
+ python3 exports_excel/main.py DEPOSITOS 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1519621
+ wait 1519621
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ DEPOSITOS completado exitosamente'
✅ DEPOSITOS completado exitosamente
+ echo '== Generando REPORTES CSV =='
== Generando REPORTES CSV ==
+ for value in "${valuescsv[@]}"
+ run_with_timeout 32A exports_csv/main.py 32A 2025/06/12 logs/csv/32A.log
+ PROCESS_NAME=32A
+ SCRIPT=exports_csv/main.py
+ REPORT=32A
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/32A.log
+ echo 'Iniciando: 32A'
Iniciando: 32A
++ date +%s
+ start_time=1749796605
+ pid=1519657
+ kill -0 1519657
+ python3 exports_csv/main.py 32A 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1519657
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1519657
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1519657
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1519657
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 1519657
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 1519657
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 1519657
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 1519657
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 1519657
+ wait 1519657
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ 32A completado exitosamente'
✅ 32A completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout BCRP-NETO-EMISORES exports_csv/main.py BCRP-NETO-EMISORES 2025/06/12 logs/csv/BCRP-NETO-EMISORES.log
+ PROCESS_NAME=BCRP-NETO-EMISORES
+ SCRIPT=exports_csv/main.py
+ REPORT=BCRP-NETO-EMISORES
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/BCRP-NETO-EMISORES.log
+ echo 'Iniciando: BCRP-NETO-EMISORES'
Iniciando: BCRP-NETO-EMISORES
++ date +%s
+ start_time=1749796650
+ pid=1519687
+ kill -0 1519687
+ python3 exports_csv/main.py BCRP-NETO-EMISORES 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1519687
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1519687
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1519687
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1519687
+ wait 1519687
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ BCRP-NETO-EMISORES completado exitosamente'
✅ BCRP-NETO-EMISORES completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout CRANDES-PAGOS exports_csv/main.py CRANDES-PAGOS 2025/06/12 logs/csv/CRANDES-PAGOS.log
+ PROCESS_NAME=CRANDES-PAGOS
+ SCRIPT=exports_csv/main.py
+ REPORT=CRANDES-PAGOS
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/CRANDES-PAGOS.log
+ echo 'Iniciando: CRANDES-PAGOS'
Iniciando: CRANDES-PAGOS
++ date +%s
+ start_time=1749796670
+ pid=1519712
+ kill -0 1519712
++ date +%s
+ python3 exports_csv/main.py CRANDES-PAGOS 2025/06/12
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1519712
+ wait 1519712
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ CRANDES-PAGOS completado exitosamente'
✅ CRANDES-PAGOS completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout VALIDAR-BALANCE exports_csv/main.py VALIDAR-BALANCE 2025/06/12 logs/csv/VALIDAR-BALANCE.log
+ PROCESS_NAME=VALIDAR-BALANCE
+ SCRIPT=exports_csv/main.py
+ REPORT=VALIDAR-BALANCE
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/VALIDAR-BALANCE.log
+ echo 'Iniciando: VALIDAR-BALANCE'
Iniciando: VALIDAR-BALANCE
++ date +%s
+ start_time=1749796675
+ pid=1519754
+ kill -0 1519754
+ python3 exports_csv/main.py VALIDAR-BALANCE 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=85
+ '[' 85 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=90
+ '[' 90 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=95
+ '[' 95 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=100
+ '[' 100 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
++ date +%s
+ elapsed_time=105
+ '[' 105 -gt 1800 ']'
+ sleep 5
+ kill -0 1519754
+ wait 1519754
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ VALIDAR-BALANCE completado exitosamente'
✅ VALIDAR-BALANCE completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout EQUIVALENCIA-LOG-TRX exports_csv/main.py EQUIVALENCIA-LOG-TRX 2025/06/12 logs/csv/EQUIVALENCIA-LOG-TRX.log
+ PROCESS_NAME=EQUIVALENCIA-LOG-TRX
+ SCRIPT=exports_csv/main.py
+ REPORT=EQUIVALENCIA-LOG-TRX
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/EQUIVALENCIA-LOG-TRX.log
+ echo 'Iniciando: EQUIVALENCIA-LOG-TRX'
Iniciando: EQUIVALENCIA-LOG-TRX
++ date +%s
+ start_time=1749796785
+ pid=1520162
+ kill -0 1520162
+ python3 exports_csv/main.py EQUIVALENCIA-LOG-TRX 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520162
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1520162
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1520162
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1520162
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 1520162
+ wait 1520162
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ EQUIVALENCIA-LOG-TRX completado exitosamente'
✅ EQUIVALENCIA-LOG-TRX completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout EQUIVALENCIA-PAGOS exports_csv/main.py EQUIVALENCIA-PAGOS 2025/06/12 logs/csv/EQUIVALENCIA-PAGOS.log
+ PROCESS_NAME=EQUIVALENCIA-PAGOS
+ SCRIPT=exports_csv/main.py
+ REPORT=EQUIVALENCIA-PAGOS
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/EQUIVALENCIA-PAGOS.log
+ echo 'Iniciando: EQUIVALENCIA-PAGOS'
Iniciando: EQUIVALENCIA-PAGOS
++ date +%s
+ start_time=1749796810
+ pid=1520293
+ kill -0 1520293
+ python3 exports_csv/main.py EQUIVALENCIA-PAGOS 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520293
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1520293
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1520293
+ wait 1520293
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ EQUIVALENCIA-PAGOS completado exitosamente'
✅ EQUIVALENCIA-PAGOS completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout EQUIVALENCIA-AHORROS exports_csv/main.py EQUIVALENCIA-AHORROS 2025/06/12 logs/csv/EQUIVALENCIA-AHORROS.log
+ PROCESS_NAME=EQUIVALENCIA-AHORROS
+ SCRIPT=exports_csv/main.py
+ REPORT=EQUIVALENCIA-AHORROS
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/EQUIVALENCIA-AHORROS.log
+ echo 'Iniciando: EQUIVALENCIA-AHORROS'
Iniciando: EQUIVALENCIA-AHORROS
++ date +%s
+ start_time=1749796825
+ pid=1520311
+ kill -0 1520311
+ python3 exports_csv/main.py EQUIVALENCIA-AHORROS 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520311
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1520311
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1520311
+ wait 1520311
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ EQUIVALENCIA-AHORROS completado exitosamente'
✅ EQUIVALENCIA-AHORROS completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout BCRP-OPERACIONES-EMISOR exports_csv/main.py BCRP-OPERACIONES-EMISOR 2025/06/12 logs/csv/BCRP-OPERACIONES-EMISOR.log
+ PROCESS_NAME=BCRP-OPERACIONES-EMISOR
+ SCRIPT=exports_csv/main.py
+ REPORT=BCRP-OPERACIONES-EMISOR
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/BCRP-OPERACIONES-EMISOR.log
+ echo 'Iniciando: BCRP-OPERACIONES-EMISOR'
Iniciando: BCRP-OPERACIONES-EMISOR
++ date +%s
+ start_time=1749796840
+ pid=1520333
+ kill -0 1520333
+ python3 exports_csv/main.py BCRP-OPERACIONES-EMISOR 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520333
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1520333
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1520333
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1520333
+ wait 1520333
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ BCRP-OPERACIONES-EMISOR completado exitosamente'
✅ BCRP-OPERACIONES-EMISOR completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout BCRP-TIPO-CUENTAS exports_csv/main.py BCRP-TIPO-CUENTAS 2025/06/12 logs/csv/BCRP-TIPO-CUENTAS.log
+ PROCESS_NAME=BCRP-TIPO-CUENTAS
+ SCRIPT=exports_csv/main.py
+ REPORT=BCRP-TIPO-CUENTAS
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/BCRP-TIPO-CUENTAS.log
+ echo 'Iniciando: BCRP-TIPO-CUENTAS'
Iniciando: BCRP-TIPO-CUENTAS
++ date +%s
+ start_time=1749796860
+ pid=1520353
+ kill -0 1520353
+ python3 exports_csv/main.py BCRP-TIPO-CUENTAS 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520353
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1520353
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1520353
+ wait 1520353
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ BCRP-TIPO-CUENTAS completado exitosamente'
✅ BCRP-TIPO-CUENTAS completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout AZULITO exports_csv/main.py AZULITO 2025/06/12 logs/csv/AZULITO.log
+ PROCESS_NAME=AZULITO
+ SCRIPT=exports_csv/main.py
+ REPORT=AZULITO
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/AZULITO.log
+ echo 'Iniciando: AZULITO'
Iniciando: AZULITO
++ date +%s
+ start_time=1749796875
+ pid=1520407
+ kill -0 1520407
+ python3 exports_csv/main.py AZULITO 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520407
+ wait 1520407
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ AZULITO completado exitosamente'
✅ AZULITO completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout UNIQUE exports_csv/main.py UNIQUE 2025/06/12 logs/csv/UNIQUE.log
+ PROCESS_NAME=UNIQUE
+ SCRIPT=exports_csv/main.py
+ REPORT=UNIQUE
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/UNIQUE.log
+ echo 'Iniciando: UNIQUE'
Iniciando: UNIQUE
++ date +%s
+ start_time=1749796880
+ pid=1520421
+ kill -0 1520421
+ python3 exports_csv/main.py UNIQUE 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520421
+ wait 1520421
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ UNIQUE completado exitosamente'
✅ UNIQUE completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout MOVISTAR exports_csv/main.py MOVISTAR 2025/06/12 logs/csv/MOVISTAR.log
+ PROCESS_NAME=MOVISTAR
+ SCRIPT=exports_csv/main.py
+ REPORT=MOVISTAR
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/MOVISTAR.log
+ echo 'Iniciando: MOVISTAR'
Iniciando: MOVISTAR
++ date +%s
+ start_time=1749796885
+ pid=1520435
+ kill -0 1520435
+ python3 exports_csv/main.py MOVISTAR 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520435
+ wait 1520435
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ MOVISTAR completado exitosamente'
✅ MOVISTAR completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout ENTEL exports_csv/main.py ENTEL 2025/06/12 logs/csv/ENTEL.log
+ PROCESS_NAME=ENTEL
+ SCRIPT=exports_csv/main.py
+ REPORT=ENTEL
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/ENTEL.log
+ echo 'Iniciando: ENTEL'
Iniciando: ENTEL
++ date +%s
+ start_time=1749796890
+ pid=1520450
+ kill -0 1520450
+ python3 exports_csv/main.py ENTEL 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520450
+ wait 1520450
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ ENTEL completado exitosamente'
✅ ENTEL completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout RETIRO-SENTINEL exports_csv/main.py RETIRO-SENTINEL 2025/06/12 logs/csv/RETIRO-SENTINEL.log
+ PROCESS_NAME=RETIRO-SENTINEL
+ SCRIPT=exports_csv/main.py
+ REPORT=RETIRO-SENTINEL
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/RETIRO-SENTINEL.log
+ echo 'Iniciando: RETIRO-SENTINEL'
Iniciando: RETIRO-SENTINEL
++ date +%s
+ start_time=1749796895
+ pid=1520464
+ kill -0 1520464
+ python3 exports_csv/main.py RETIRO-SENTINEL 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520464
+ wait 1520464
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ RETIRO-SENTINEL completado exitosamente'
✅ RETIRO-SENTINEL completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout RETIRO-WU-HUB exports_csv/main.py RETIRO-WU-HUB 2025/06/12 logs/csv/RETIRO-WU-HUB.log
+ PROCESS_NAME=RETIRO-WU-HUB
+ SCRIPT=exports_csv/main.py
+ REPORT=RETIRO-WU-HUB
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/RETIRO-WU-HUB.log
+ echo 'Iniciando: RETIRO-WU-HUB'
Iniciando: RETIRO-WU-HUB
++ date +%s
+ start_time=1749796900
+ pid=1520479
+ kill -0 1520479
+ python3 exports_csv/main.py RETIRO-WU-HUB 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520479
+ wait 1520479
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ RETIRO-WU-HUB completado exitosamente'
✅ RETIRO-WU-HUB completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout BCRP-BALANCES exports_csv/main.py BCRP-BALANCES 2025/06/12 logs/csv/BCRP-BALANCES.log
+ PROCESS_NAME=BCRP-BALANCES
+ SCRIPT=exports_csv/main.py
+ REPORT=BCRP-BALANCES
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/BCRP-BALANCES.log
+ echo 'Iniciando: BCRP-BALANCES'
Iniciando: BCRP-BALANCES
++ date +%s
+ start_time=1749796905
+ pid=1520493
+ kill -0 1520493
+ python3 exports_csv/main.py BCRP-BALANCES 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=85
+ '[' 85 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=90
+ '[' 90 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=95
+ '[' 95 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=100
+ '[' 100 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=105
+ '[' 105 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
++ date +%s
+ elapsed_time=110
+ '[' 110 -gt 1800 ']'
+ sleep 5
+ kill -0 1520493
+ wait 1520493
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ BCRP-BALANCES completado exitosamente'
✅ BCRP-BALANCES completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout QR-NIUBIZ exports_csv/main.py QR-NIUBIZ 2025/06/12 logs/csv/QR-NIUBIZ.log
+ PROCESS_NAME=QR-NIUBIZ
+ SCRIPT=exports_csv/main.py
+ REPORT=QR-NIUBIZ
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/QR-NIUBIZ.log
+ echo 'Iniciando: QR-NIUBIZ'
Iniciando: QR-NIUBIZ
++ date +%s
+ start_time=1749797020
+ pid=1521161
+ kill -0 1521161
+ python3 exports_csv/main.py QR-NIUBIZ 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1521161
+ wait 1521161
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ QR-NIUBIZ completado exitosamente'
✅ QR-NIUBIZ completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout QR-IZIPAY exports_csv/main.py QR-IZIPAY 2025/06/12 logs/csv/QR-IZIPAY.log
+ PROCESS_NAME=QR-IZIPAY
+ SCRIPT=exports_csv/main.py
+ REPORT=QR-IZIPAY
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/QR-IZIPAY.log
+ echo 'Iniciando: QR-IZIPAY'
Iniciando: QR-IZIPAY
++ date +%s
+ start_time=1749797025
+ pid=1521179
+ kill -0 1521179
+ python3 exports_csv/main.py QR-IZIPAY 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1521179
+ wait 1521179
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ QR-IZIPAY completado exitosamente'
✅ QR-IZIPAY completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout MTX-TRANSACTION exports_csv/main.py MTX-TRANSACTION 2025/06/12 logs/csv/MTX-TRANSACTION.log
+ PROCESS_NAME=MTX-TRANSACTION
+ SCRIPT=exports_csv/main.py
+ REPORT=MTX-TRANSACTION
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/MTX-TRANSACTION.log
+ echo 'Iniciando: MTX-TRANSACTION'
Iniciando: MTX-TRANSACTION
++ date +%s
+ start_time=1749797030
+ pid=1521193
+ kill -0 1521193
+ python3 exports_csv/main.py MTX-TRANSACTION 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1800 ']'
+ sleep 5
+ kill -0 1521193
+ wait 1521193
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ MTX-TRANSACTION completado exitosamente'
✅ MTX-TRANSACTION completado exitosamente
+ for value in "${valuescsv[@]}"
+ run_with_timeout LOG-TRANSACCIONES exports_csv/main.py LOG-TRANSACCIONES 2025/06/12 logs/csv/LOG-TRANSACCIONES.log
+ PROCESS_NAME=LOG-TRANSACCIONES
+ SCRIPT=exports_csv/main.py
+ REPORT=LOG-TRANSACCIONES
+ DATE=2025/06/12
+ LOG_FILE=logs/csv/LOG-TRANSACCIONES.log
+ echo 'Iniciando: LOG-TRANSACCIONES'
Iniciando: LOG-TRANSACCIONES
++ date +%s
+ start_time=1749797105
+ pid=1521548
+ kill -0 1521548
+ python3 exports_csv/main.py LOG-TRANSACCIONES 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1521548
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1521548
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1521548
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1521548
+ wait 1521548
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ LOG-TRANSACCIONES completado exitosamente'
✅ LOG-TRANSACCIONES completado exitosamente
+ echo '== Procesando LOG TRANSACCIONES =='
== Procesando LOG TRANSACCIONES ==
+ cd /home/<USER>/generate/log_transacciones/
+ mkdir -p output/********
+ run_with_timeout LOG-TRANSACCIONES-PROCESAR procesar.py '' 2025/06/12 /home/<USER>/generate/logs/log_transacciones/LOG-TRANSACCIONES.log
+ PROCESS_NAME=LOG-TRANSACCIONES-PROCESAR
+ SCRIPT=procesar.py
+ REPORT=
+ DATE=2025/06/12
+ LOG_FILE=/home/<USER>/generate/logs/log_transacciones/LOG-TRANSACCIONES.log
+ echo 'Iniciando: LOG-TRANSACCIONES-PROCESAR'
Iniciando: LOG-TRANSACCIONES-PROCESAR
++ date +%s
+ start_time=**********
+ pid=1521703
+ kill -0 1521703
+ python3 procesar.py 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1521703
+ wait 1521703
+ return_code=1
+ '[' 1 -eq 0 ']'
+ echo '❌ LOG-TRANSACCIONES-PROCESAR falló (código: 1)'
❌ LOG-TRANSACCIONES-PROCESAR falló (código: 1)
+ echo '== Procesando ACCOUNT BALANCES =='
== Procesando ACCOUNT BALANCES ==
+ cd /home/<USER>/generate/account_balance/
+ run_with_timeout ACCOUNT-BALANCES main.py '' 2025/06/12 /home/<USER>/generate/logs/account_balances/ACC-BALANCES.log
+ PROCESS_NAME=ACCOUNT-BALANCES
+ SCRIPT=main.py
+ REPORT=
+ DATE=2025/06/12
+ LOG_FILE=/home/<USER>/generate/logs/account_balances/ACC-BALANCES.log
+ echo 'Iniciando: ACCOUNT-BALANCES'
Iniciando: ACCOUNT-BALANCES
++ date +%s
+ start_time=**********
+ pid=1521716
+ kill -0 1521716
+ python3 main.py 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1521716
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1521716
+ wait 1521716
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ ACCOUNT-BALANCES completado exitosamente'
✅ ACCOUNT-BALANCES completado exitosamente
+ echo '== Generando REPORTES 32x =='
== Generando REPORTES 32x ==
+ cd /home/<USER>/generate/reports32a-b/
+ for value in "${values32[@]}"
+ echo 'Iniciando: 32A'
Iniciando: 32A
+ run_with_timeout 32A-32 main.py 32A 2025/06/12 /home/<USER>/generate/logs/reports32a_b/32A.log
+ PROCESS_NAME=32A-32
+ SCRIPT=main.py
+ REPORT=32A
+ DATE=2025/06/12
+ LOG_FILE=/home/<USER>/generate/logs/reports32a_b/32A.log
+ echo 'Iniciando: 32A-32'
Iniciando: 32A-32
++ date +%s
+ start_time=**********
+ pid=1521796
+ kill -0 1521796
+ python3 main.py 32A 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1521796
+ wait 1521796
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ 32A-32 completado exitosamente'
✅ 32A-32 completado exitosamente
+ echo '== Ejecutando GOPAY =='
== Ejecutando GOPAY ==
+ cd /home/<USER>/generate/mysql_reports/GOPAY
+ run_with_timeout GOPAY main.py '' 2025/06/12 /home/<USER>/generate/logs/mysql_reports/GOPAY.log
+ PROCESS_NAME=GOPAY
+ SCRIPT=main.py
+ REPORT=
+ DATE=2025/06/12
+ LOG_FILE=/home/<USER>/generate/logs/mysql_reports/GOPAY.log
+ echo 'Iniciando: GOPAY'
Iniciando: GOPAY
++ date +%s
+ start_time=1749797145
+ pid=1521831
+ kill -0 1521831
+ python3 main.py 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1521831
+ wait 1521831
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ GOPAY completado exitosamente'
✅ GOPAY completado exitosamente
+ echo '== Ejecutando FULLCARGAS =='
== Ejecutando FULLCARGAS ==
+ cd /home/<USER>/generate/mysql_reports/Fullcargas
+ run_with_timeout FULLCARGAS main.py '' 2025/06/12 /home/<USER>/generate/logs/mysql_reports/FULLCARGAS.log
+ PROCESS_NAME=FULLCARGAS
+ SCRIPT=main.py
+ REPORT=
+ DATE=2025/06/12
+ LOG_FILE=/home/<USER>/generate/logs/mysql_reports/FULLCARGAS.log
+ echo 'Iniciando: FULLCARGAS'
Iniciando: FULLCARGAS
++ date +%s
+ start_time=1749797150
+ pid=1521914
+ kill -0 1521914
+ python3 main.py 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1521914
+ wait 1521914
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ FULLCARGAS completado exitosamente'
✅ FULLCARGAS completado exitosamente
+ echo '== Ejecutando SERVICIOS-WU =='
== Ejecutando SERVICIOS-WU ==
+ cd /home/<USER>/generate/mysql_reports/Servicios-WU
+ run_with_timeout SERVICIOS-WU main.py '' 2025/06/12 /home/<USER>/generate/logs/mysql_reports/SERVICIOS-WU.log
+ PROCESS_NAME=SERVICIOS-WU
+ SCRIPT=main.py
+ REPORT=
+ DATE=2025/06/12
+ LOG_FILE=/home/<USER>/generate/logs/mysql_reports/SERVICIOS-WU.log
+ echo 'Iniciando: SERVICIOS-WU'
Iniciando: SERVICIOS-WU
++ date +%s
+ start_time=1749797155
+ pid=1521940
+ kill -0 1521940
+ python3 main.py 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1521940
+ wait 1521940
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ SERVICIOS-WU completado exitosamente'
✅ SERVICIOS-WU completado exitosamente
+ echo '== Ejecutando CONCILIACION BIM =='
== Ejecutando CONCILIACION BIM ==
+ cd /home/<USER>/generate/reporte_conciliacion/
+ run_with_timeout CONCILIACION-BIM main.py '' 2025/06/12 /home/<USER>/generate/logs/reporte_conciliacion/CONCILIACION-BIM.log
+ PROCESS_NAME=CONCILIACION-BIM
+ SCRIPT=main.py
+ REPORT=
+ DATE=2025/06/12
+ LOG_FILE=/home/<USER>/generate/logs/reporte_conciliacion/CONCILIACION-BIM.log
+ echo 'Iniciando: CONCILIACION-BIM'
Iniciando: CONCILIACION-BIM
++ date +%s
+ start_time=1749797160
+ pid=1522145
+ kill -0 1522145
+ python3 main.py 2025/06/12
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=5
+ '[' 5 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=10
+ '[' 10 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=15
+ '[' 15 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=20
+ '[' 20 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=25
+ '[' 25 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=30
+ '[' 30 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=35
+ '[' 35 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=40
+ '[' 40 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=45
+ '[' 45 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=50
+ '[' 50 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=55
+ '[' 55 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=60
+ '[' 60 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=65
+ '[' 65 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=70
+ '[' 70 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=75
+ '[' 75 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=80
+ '[' 80 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=85
+ '[' 85 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=90
+ '[' 90 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=95
+ '[' 95 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=100
+ '[' 100 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=105
+ '[' 105 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=110
+ '[' 110 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=115
+ '[' 115 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=120
+ '[' 120 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=125
+ '[' 125 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=130
+ '[' 130 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=135
+ '[' 135 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=140
+ '[' 140 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=145
+ '[' 145 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=150
+ '[' 150 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=155
+ '[' 155 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=160
+ '[' 160 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=165
+ '[' 165 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=170
+ '[' 170 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=175
+ '[' 175 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=180
+ '[' 180 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=185
+ '[' 185 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=190
+ '[' 190 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=195
+ '[' 195 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=200
+ '[' 200 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=205
+ '[' 205 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=210
+ '[' 210 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=215
+ '[' 215 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=220
+ '[' 220 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=225
+ '[' 225 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=230
+ '[' 230 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=235
+ '[' 235 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=240
+ '[' 240 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=245
+ '[' 245 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=250
+ '[' 250 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=255
+ '[' 255 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=260
+ '[' 260 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=265
+ '[' 265 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=270
+ '[' 270 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=275
+ '[' 275 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=280
+ '[' 280 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=285
+ '[' 285 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=290
+ '[' 290 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=295
+ '[' 295 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=300
+ '[' 300 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=305
+ '[' 305 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=310
+ '[' 310 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=315
+ '[' 315 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=320
+ '[' 320 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=325
+ '[' 325 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=330
+ '[' 330 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=335
+ '[' 335 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=340
+ '[' 340 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=345
+ '[' 345 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=350
+ '[' 350 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=355
+ '[' 355 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=360
+ '[' 360 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=365
+ '[' 365 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=370
+ '[' 370 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=375
+ '[' 375 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=380
+ '[' 380 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=385
+ '[' 385 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=390
+ '[' 390 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=395
+ '[' 395 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=400
+ '[' 400 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=405
+ '[' 405 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=410
+ '[' 410 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=415
+ '[' 415 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=420
+ '[' 420 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=425
+ '[' 425 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=430
+ '[' 430 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=435
+ '[' 435 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=440
+ '[' 440 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=445
+ '[' 445 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=450
+ '[' 450 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=455
+ '[' 455 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=460
+ '[' 460 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=465
+ '[' 465 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=470
+ '[' 470 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=475
+ '[' 475 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=480
+ '[' 480 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=485
+ '[' 485 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=490
+ '[' 490 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=495
+ '[' 495 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=500
+ '[' 500 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=505
+ '[' 505 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=510
+ '[' 510 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=515
+ '[' 515 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=520
+ '[' 520 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=525
+ '[' 525 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=530
+ '[' 530 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=535
+ '[' 535 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=540
+ '[' 540 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=545
+ '[' 545 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=550
+ '[' 550 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=555
+ '[' 555 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=560
+ '[' 560 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=565
+ '[' 565 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=570
+ '[' 570 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=575
+ '[' 575 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=580
+ '[' 580 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=585
+ '[' 585 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=590
+ '[' 590 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=595
+ '[' 595 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=600
+ '[' 600 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=605
+ '[' 605 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=610
+ '[' 610 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=615
+ '[' 615 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=620
+ '[' 620 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=625
+ '[' 625 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=630
+ '[' 630 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=635
+ '[' 635 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=640
+ '[' 640 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=645
+ '[' 645 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=650
+ '[' 650 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=655
+ '[' 655 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=660
+ '[' 660 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=665
+ '[' 665 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=670
+ '[' 670 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=675
+ '[' 675 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=680
+ '[' 680 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=685
+ '[' 685 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=690
+ '[' 690 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=695
+ '[' 695 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=700
+ '[' 700 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=705
+ '[' 705 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=710
+ '[' 710 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=715
+ '[' 715 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=720
+ '[' 720 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=725
+ '[' 725 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=730
+ '[' 730 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=735
+ '[' 735 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=740
+ '[' 740 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=745
+ '[' 745 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=750
+ '[' 750 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=755
+ '[' 755 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=760
+ '[' 760 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=765
+ '[' 765 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=770
+ '[' 770 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=775
+ '[' 775 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=780
+ '[' 780 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=785
+ '[' 785 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=790
+ '[' 790 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=795
+ '[' 795 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=801
+ '[' 801 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=806
+ '[' 806 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=811
+ '[' 811 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=816
+ '[' 816 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=821
+ '[' 821 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=826
+ '[' 826 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=831
+ '[' 831 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=836
+ '[' 836 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=841
+ '[' 841 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=846
+ '[' 846 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=851
+ '[' 851 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=856
+ '[' 856 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=861
+ '[' 861 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=866
+ '[' 866 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=871
+ '[' 871 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=876
+ '[' 876 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=881
+ '[' 881 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=886
+ '[' 886 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=891
+ '[' 891 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=896
+ '[' 896 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=901
+ '[' 901 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=906
+ '[' 906 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=911
+ '[' 911 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=916
+ '[' 916 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=921
+ '[' 921 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=926
+ '[' 926 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=931
+ '[' 931 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=936
+ '[' 936 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=941
+ '[' 941 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=946
+ '[' 946 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=951
+ '[' 951 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=956
+ '[' 956 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=961
+ '[' 961 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=966
+ '[' 966 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=971
+ '[' 971 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=976
+ '[' 976 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=981
+ '[' 981 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=986
+ '[' 986 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=991
+ '[' 991 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=996
+ '[' 996 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1001
+ '[' 1001 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1006
+ '[' 1006 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1011
+ '[' 1011 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1016
+ '[' 1016 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1021
+ '[' 1021 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1026
+ '[' 1026 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1031
+ '[' 1031 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1036
+ '[' 1036 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1041
+ '[' 1041 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1046
+ '[' 1046 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1051
+ '[' 1051 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1056
+ '[' 1056 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1061
+ '[' 1061 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1066
+ '[' 1066 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1071
+ '[' 1071 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1076
+ '[' 1076 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1081
+ '[' 1081 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1086
+ '[' 1086 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1091
+ '[' 1091 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1096
+ '[' 1096 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1101
+ '[' 1101 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1106
+ '[' 1106 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1111
+ '[' 1111 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1116
+ '[' 1116 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1121
+ '[' 1121 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1126
+ '[' 1126 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1131
+ '[' 1131 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1136
+ '[' 1136 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1141
+ '[' 1141 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1146
+ '[' 1146 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1151
+ '[' 1151 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1156
+ '[' 1156 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1161
+ '[' 1161 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1166
+ '[' 1166 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1171
+ '[' 1171 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1176
+ '[' 1176 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1181
+ '[' 1181 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1186
+ '[' 1186 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1191
+ '[' 1191 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1196
+ '[' 1196 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1201
+ '[' 1201 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1206
+ '[' 1206 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1211
+ '[' 1211 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1216
+ '[' 1216 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1221
+ '[' 1221 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1226
+ '[' 1226 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1231
+ '[' 1231 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1236
+ '[' 1236 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1241
+ '[' 1241 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1246
+ '[' 1246 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1251
+ '[' 1251 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1256
+ '[' 1256 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1261
+ '[' 1261 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1266
+ '[' 1266 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1271
+ '[' 1271 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1276
+ '[' 1276 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1281
+ '[' 1281 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1286
+ '[' 1286 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1291
+ '[' 1291 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1296
+ '[' 1296 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1301
+ '[' 1301 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1306
+ '[' 1306 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1311
+ '[' 1311 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1316
+ '[' 1316 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
++ date +%s
+ elapsed_time=1321
+ '[' 1321 -gt 1800 ']'
+ sleep 5
+ kill -0 1522145
+ wait 1522145
+ return_code=0
+ '[' 0 -eq 0 ']'
+ echo '✅ CONCILIACION-BIM completado exitosamente'
✅ CONCILIACION-BIM completado exitosamente
+ echo '== Ejecutando REPORTE INTEROPERABILIDAD NIUBIZ =='
== Ejecutando REPORTE INTEROPERABILIDAD NIUBIZ ==
+ log_trx_file=TR-********.csv
+ mtx_trx_header_file=MTX_TRANSACTION_HEADER_********.csv
+ log_trx_new_name=LOG_TRX_FINAL.csv
+ mtx_trx_new_name=MTX_TRANSACTION_HEADER.csv
+ mkdir -p /home/<USER>/output/load_rds
+ '[' -f /home/<USER>/output/csv/TR-********.csv ']'
+ mv /home/<USER>/output/csv/TR-********.csv /home/<USER>/output/load_rds/LOG_TRX_FINAL.csv
+ echo '✅ Archivo TR-********.csv movido a LOG_TRX_FINAL.csv'
✅ Archivo TR-********.csv movido a LOG_TRX_FINAL.csv
+ '[' -f /home/<USER>/output/csv/MTX_TRANSACTION_HEADER_********.csv ']'
+ mv /home/<USER>/output/csv/MTX_TRANSACTION_HEADER_********.csv /home/<USER>/output/load_rds/MTX_TRANSACTION_HEADER.csv
+ echo '✅ Archivo MTX_TRANSACTION_HEADER_********.csv movido a MTX_TRANSACTION_HEADER.csv'
✅ Archivo MTX_TRANSACTION_HEADER_********.csv movido a MTX_TRANSACTION_HEADER.csv
+ cd /home/<USER>/generate/prepare_rds/
+ run_with_timeout CARGAR-RDS read_csv_sql.py /home/<USER>/output/load_rds '' /home/<USER>/generate/logs/prepare_rds/CARGAR-RDS.log
+ PROCESS_NAME=CARGAR-RDS
+ SCRIPT=read_csv_sql.py
+ REPORT=/home/<USER>/output/load_rds
+ DATE=
+ LOG_FILE=/home/<USER>/generate/logs/prepare_rds/CARGAR-RDS.log
+ echo 'Iniciando: CARGAR-RDS'
Iniciando: CARGAR-RDS
++ date +%s
+ start_time=1749798486
+ pid=1524683
+ kill -0 1524683
+ python3 read_csv_sql.py /home/<USER>/output/load_rds
++ date +%s
+ elapsed_time=0
+ '[' 0 -gt 1800 ']'
+ sleep 5
+ kill -0 1524683
+ wait 1524683
+ return_code=1
+ '[' 1 -eq 0 ']'
+ echo '❌ CARGAR-RDS falló (código: 1)'
❌ CARGAR-RDS falló (código: 1)
+ cd /home/<USER>/generate/
+ echo ''

+ echo '== RESUMEN DE ESTADO =='
== RESUMEN DE ESTADO ==
+ for value in "${values[@]}"
+ grep -qi error logs/excel/BITEL-POST.log
+ echo '✅ BITEL-POST'
✅ BITEL-POST
+ for value in "${values[@]}"
+ grep -qi error logs/excel/BITEL-PRE.log
+ echo '✅ BITEL-PRE'
✅ BITEL-PRE
+ for value in "${values[@]}"
+ grep -qi error logs/excel/SERVICE-PROVIDER.log
+ echo '✅ SERVICE-PROVIDER'
✅ SERVICE-PROVIDER
+ for value in "${values[@]}"
+ grep -qi error logs/excel/SERVICIOS-DIRECTOS.log
+ echo '✅ SERVICIOS-DIRECTOS'
✅ SERVICIOS-DIRECTOS
+ for value in "${values[@]}"
+ grep -qi error logs/excel/TRAZA-FEE.log
+ echo '✅ TRAZA-FEE'
✅ TRAZA-FEE
+ for value in "${values[@]}"
+ grep -qi error logs/excel/RETIROS.log
+ echo '❌ RETIROS (ver logs/excel/RETIROS.log)'
❌ RETIROS (ver logs/excel/RETIROS.log)
+ for value in "${values[@]}"
+ grep -qi error logs/excel/DEPOSITOS.log
+ echo '❌ DEPOSITOS (ver logs/excel/DEPOSITOS.log)'
❌ DEPOSITOS (ver logs/excel/DEPOSITOS.log)
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/32A.log
+ echo '✅ 32A'
✅ 32A
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/BCRP-NETO-EMISORES.log
+ echo '✅ BCRP-NETO-EMISORES'
✅ BCRP-NETO-EMISORES
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/CRANDES-PAGOS.log
+ echo '✅ CRANDES-PAGOS'
✅ CRANDES-PAGOS
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/VALIDAR-BALANCE.log
+ echo '✅ VALIDAR-BALANCE'
✅ VALIDAR-BALANCE
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/EQUIVALENCIA-LOG-TRX.log
+ echo '✅ EQUIVALENCIA-LOG-TRX'
✅ EQUIVALENCIA-LOG-TRX
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/EQUIVALENCIA-PAGOS.log
+ echo '✅ EQUIVALENCIA-PAGOS'
✅ EQUIVALENCIA-PAGOS
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/EQUIVALENCIA-AHORROS.log
+ echo '✅ EQUIVALENCIA-AHORROS'
✅ EQUIVALENCIA-AHORROS
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/BCRP-OPERACIONES-EMISOR.log
+ echo '✅ BCRP-OPERACIONES-EMISOR'
✅ BCRP-OPERACIONES-EMISOR
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/BCRP-TIPO-CUENTAS.log
+ echo '✅ BCRP-TIPO-CUENTAS'
✅ BCRP-TIPO-CUENTAS
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/AZULITO.log
+ echo '✅ AZULITO'
✅ AZULITO
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/UNIQUE.log
+ echo '✅ UNIQUE'
✅ UNIQUE
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/MOVISTAR.log
+ echo '✅ MOVISTAR'
✅ MOVISTAR
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/ENTEL.log
+ echo '✅ ENTEL'
✅ ENTEL
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/RETIRO-SENTINEL.log
+ echo '✅ RETIRO-SENTINEL'
✅ RETIRO-SENTINEL
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/RETIRO-WU-HUB.log
+ echo '✅ RETIRO-WU-HUB'
✅ RETIRO-WU-HUB
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/BCRP-BALANCES.log
+ echo '✅ BCRP-BALANCES'
✅ BCRP-BALANCES
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/QR-NIUBIZ.log
+ echo '❌ QR-NIUBIZ (ver logs/csv/QR-NIUBIZ.log)'
❌ QR-NIUBIZ (ver logs/csv/QR-NIUBIZ.log)
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/QR-IZIPAY.log
+ echo '❌ QR-IZIPAY (ver logs/csv/QR-IZIPAY.log)'
❌ QR-IZIPAY (ver logs/csv/QR-IZIPAY.log)
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/MTX-TRANSACTION.log
+ echo '✅ MTX-TRANSACTION'
✅ MTX-TRANSACTION
+ for value in "${valuescsv[@]}"
+ grep -qi error logs/csv/LOG-TRANSACCIONES.log
+ echo '✅ LOG-TRANSACCIONES'
✅ LOG-TRANSACCIONES
+ echo '== FIN DE EJECUCIÓN =='
== FIN DE EJECUCIÓN ==
Fin de la malla
